import { AppDataSource } from '@/config/config'
import { TblCities } from '@/entities'
import { TblDistricts } from '@/entities'
import { TblCommunes } from '@/entities'

const cityRepository = () => AppDataSource.getRepository(TblCities);
const districtRepository = () => AppDataSource.getRepository(TblDistricts);
const communeRepository = () => AppDataSource.getRepository(TblCommunes);

export const getAllCity = async (request) => {    
    const event = await cityRepository().find();
    
    return {
        status: 200,
        code: '',
        data: event
    };
}

export const listDistrictByCity = async (request) => {
    const cityId = request.params.id

    if(cityId){
        const event = await districtRepository().find({ where: { cityId: cityId } });
    
        return {
            status: 200,
            code: '',
            data: event
        };
    }
    
    return {
        status: 200,
        code: '',
        data: []
    };
}

export const lisCommuneByDistrict = async (request) => {
    const districtId = request.params.id

    if(districtId){
        const event = await communeRepository().find({ where: { districtId: districtId } });
    
        return {
            status: 200,
            code: '',
            data: event
        };
    }
    
    return {
        status: 200,
        code: '',
        data: []
    };
}
