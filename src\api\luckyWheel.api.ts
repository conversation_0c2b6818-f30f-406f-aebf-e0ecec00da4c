import express, { Request, Response } from 'express'
import { luckyWheelService } from '@/services'
import { AuthorizedUserRequest } from '@/middlewares/auth'
import { AppDataSource } from '@/config/config'
import { WaterUserGift } from '@/entities'

const waterUserGiftRepo = () => AppDataSource.getRepository(WaterUserGift)

const spinLuckyWheelHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {
    const { user } = req.authorizedUser
    const { campaignId, type, gameId } = req.body
    if (!campaignId) {
      return res.status(200).json({ status: 200, error_code: 1, message: 'Missing campaignId' })
    }
    if (!type || isNaN(parseInt(type)) || parseInt(type) == 0) {
      return res.status(200).json({ status: 200, error_code: 1, message: 'Missing Type' })
    }
    const result = await luckyWheelService.spinLuckyWheel(user.id, parseInt(campaignId) || 0, user.tokyoId, user.bizId, parseInt(type), parseInt(gameId) || 0)
    return res.status(200).json({
      status: 200,
      error_code: 0,
      data: result
    })
  } catch (e) {
    return res.status(200).json({ status: 200, error_code: 1, message: e.message })
  }
}

const getActivePrizesHandler = async (req: express.Request, res: express.Response) => {
  try {
    const { campaignId, gameId } = req.query
    const prizes = await luckyWheelService.getActivePrizes(parseInt(campaignId as string) || 0, parseInt(gameId as string) || 0)
    return res.status(200).json({
      status: 200,
      error_code: 0,
      data: { prizes }
    })
  } catch (e) {
    return res.status(200).json({ status: 200, error_code: 1, message: e.message })
  }
}

const addSpinCountHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {
    const { user } = req.authorizedUser
    const { campaignId, gameId } = req.body
    if (!campaignId) {
      return res.status(200).json({ status: 200, error_code: 1, message: 'Missing campaignId' })
    }
    if (!gameId || isNaN(parseInt(gameId)) || parseInt(gameId) == 0) {
      return res.status(200).json({ status: 200, error_code: 1, message: 'Missing gameId' })
    }
    const spinCounts = await luckyWheelService.addSpinCount(user.id, parseInt(campaignId) || 0, parseInt(gameId) || 0)
    return res.status(200).json({
      status: 200,
      error_code: 0,
      message: 'Cộng lượt quay thành công',
      data: {
        spin_counts: spinCounts
      }
    })
  } catch (e) {
    return res.status(200).json({ status: 200, error_code: 1, message: e.message })
  }
}

const getSpinHistoryHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {
    const { user } = req.authorizedUser
    const { campaignId, type, gameId } = req.query
    console.log('getSpinHistoryHandler', user.id, campaignId, type, gameId);
    const history = await luckyWheelService.getSpinHistoryByUser(user.id, parseInt(campaignId as string) || 0, parseInt(gameId as string) || 0, parseInt(type as string) || 0)
    // const waterGifts = await waterUserGiftRepo().find({ 
    //   where: { 
    //     userId: user.id,
    //     campaignId: parseInt(campaignId as string) || 0,
    //     active: 0,
    //     times: 0
    //   }
    // })
    
    const result = history.map(item => ({
      spin_time: item.spinTime,
      prize_name: item.voucherName ? item.voucherName + ' ' + item.voucherCode : (item.prize?.name || null)
    }))

    // Thêm thông tin từ waterUserGift
    // waterGifts.forEach(gift => {
    //   result.push({
    //     spin_time: gift.createdAt,
    //     prize_name: `Mã dự thưởng: ${gift.rewardCode}`
    //   })
    // })

    // Sắp xếp theo thời gian giảm dần
    result.sort((a, b) => new Date(b.spin_time).getTime() - new Date(a.spin_time).getTime())

    return res.status(200).json({
      status: 200,
      error_code: 0,
      data: result
    })
  } catch (e) {
    return res.status(200).json({ status: 200, error_code: 1, message: e.message })
  }
}

const getDailyStatsController = async (req: Request, res: Response) => {
  try {
    const { campaignId, gameId } = req.query
    if (!campaignId) {
      return res.status(200).json({ status: 200, error_code: 1, message: 'Missing campaignId' })
    }
    const stats = await luckyWheelService.getDailyStats(parseInt(campaignId as string) || 0, parseInt(gameId as string) || 0)
    return res.status(200).json({
      status: 200,
      error_code: 0,
      data: stats
    })
  } catch (error) {
    return res.status(500).json({
      status: 500,
      error_code: 1,
      message: error.message
    })
  }
}

const getInfoCustomerHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {
    const { user } = req.authorizedUser
    const { phone } = req.body
    if (!phone) {
      return res.status(200).json({ status: 200, error_code: 1, message: 'Missing phone' })
    }
    const result = await luckyWheelService.getInfoCustomerFromBizfly(user.id, phone)
    return res.status(200).json({
      status: 200,
      error_code: 0,
      data: result
    })
  } catch (e) {
    return res.status(200).json({ status: 200, error_code: 1, message: e.message })
  }
}

export { spinLuckyWheelHandler, getActivePrizesHandler, addSpinCountHandler, getSpinHistoryHandler, getDailyStatsController, getInfoCustomerHandler }
