import express from 'express'
import { locationAPI } from '@/api'
import { authenticate } from '@/middlewares/auth'

// event management router
const locationRouter = express.Router();

locationRouter.get('/city', authenticate, locationAPI.getAllCity);
locationRouter.get('/city/:id/district', authenticate, locationAPI.listDistrictByCity);
locationRouter.get('/district/:id/commune', authenticate, locationAPI.lisCommuneByDistrict);

// Export router
const router = express.Router();

router.use('/location', locationRouter);


export default router
