import { JWT } from '@/config/config'
import { accessToken, jwtPayloadToAuthenticationToken, jwtPayloadToResourceToken, refreshToken, resourceToken } from '@/transforms/token'
import { AuthenticationToken, ResourceProvider, ResourceToken } from '@/types/models/token'
import jwt from 'jsonwebtoken'
import { redisService } from '.'

interface JWTService {
  generateResourceToken: (path: string, provider: ResourceProvider) => string
  verifyResourceToken: (token: string) => ResourceToken | null

  generateAccessToken: (id: number, role: number) => string
  generateRefreshToken: (id: number, role: number) => string
  verifyAuthenticationToken: (token: string) => AuthenticationToken | null
  decodeAuthenticationToken: (token: string) => AuthenticationToken | null
  isRevokedAuthenticationToken: (jti: string) => Promise<boolean>
  revokeAuthenticationToken: (token: string) => Promise<boolean>
}

const service: JWTService = {
  generateResourceToken: (path: string, provider: ResourceProvider) => {
    return jwt.sign(resourceToken(path, provider), JWT.SECRET)
  },
  verifyResourceToken: (token: string) => {
    const payload = jwt.verify(token, JWT.SECRET)
    if (typeof payload === 'string') {
      return null
    }
    return jwtPayloadToResourceToken(payload)
  },

  generateAccessToken: (id, role) => {
    return jwt.sign(accessToken(id, role), JWT.SECRET)
  },
  generateRefreshToken: (id, role) => {
    return jwt.sign(refreshToken(id, role), JWT.SECRET)
  },
  verifyAuthenticationToken: (token: string) => {
    const payload = jwt.verify(token, JWT.SECRET)
    if (typeof payload === 'string') {
      return null
    }
    return jwtPayloadToAuthenticationToken(payload)
  },
  decodeAuthenticationToken: (token: string) => {
    try {
      const payload = jwt.verify(token, JWT.SECRET, { ignoreNotBefore: true, ignoreExpiration: true })
      if (typeof payload === 'string') {
        return null
      }
      return jwtPayloadToAuthenticationToken(payload)
    } catch {
      return null
    }
  },
  isRevokedAuthenticationToken: async (jti) => {
    const result = await redisService.Shared.getClient().get(`REVOKED_JWT_${jti}`)
    return result === 'revoked'
  },
  revokeAuthenticationToken: async (token) => {
    try {
      const payload = jwt.decode(token)
      if (typeof payload === 'string' || !payload.jti || !payload.exp) {
        return false
      }

      const now = Math.floor(Date.now() / 1000)
      if (now >= payload.exp) {
        return false
      }

      const key = `REVOKED_JWT_${payload.jti}`
      await redisService.Shared.getClient().set(key, 'revoked')
      await redisService.Shared.getClient().pexpireat(key, payload.exp * 1000)

      return true
    } catch {
      return false
    }
  }
}
export default service
