'use strict'

import bcrypt from 'bcryptjs'

const saltRounds = 10
const maxLength = 18
const minLength = 6
const uppercaseMinCount = 1
const lowercaseMinCount = 1
const numberMinCount = 1
const specialMinCount = 1
const UPPERCASE_RE = /([A-Z])/g
const LOWERCASE_RE = /([a-z])/g
const NUMBER_RE = /([\d])/g
const SPECIAL_CHAR_RE = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/g
const NON_REPEATING_CHAR_RE = /([\w\d\?\-])\1{2,}/g

const PasswordService = {
  defaultPassword: 'Password@123',

  validateRules(password) {
    let result = []
    if (password.length < minLength) result.push(`Password must greater than or equal ${minLength} characters`)

    if (password.length > maxLength) result.push(`Password must less than or equal ${maxLength} characters`)

    var uc = password.match(UPPERCASE_RE)
    if (!(uc && uc.length >= uppercaseMinCount)) result.push(`Password must be at least ${uppercaseMinCount} characters in upper case`)

    var lc = password.match(LOWERCASE_RE)
    if (!(lc && lc.length >= lowercaseMinCount)) result.push(`Password must be at least ${lowercaseMinCount} characters in lower case`)

    var n = password.match(NUMBER_RE)
    if (!(n && n.length >= numberMinCount)) result.push(`Password must be at least ${numberMinCount} characters in number`)

    var sc = password.match(SPECIAL_CHAR_RE)
    if (!(sc && sc.length >= specialMinCount)) result.push(`Password must be at least ${specialMinCount} special characters`)

    return result
  },

  async hashPassword(plaintextPassword): Promise<string> {
    return await bcrypt.hashSync(plaintextPassword, saltRounds)
  },

  async comparePassword(plaintextPassword, hash): Promise<boolean> {
    return await bcrypt.compareSync(plaintextPassword, hash)
  }
}

export default PasswordService
