import { AppDataSource } from '@/config/config'
import { TblG<PERSON>s, TblGameFiles, TblGifts, TblUserEventGift, TblGameLog } from '@/entities'
import { AuthorizedUserRequest } from '@/middlewares/auth'
import { GameRequest } from '@/types/requests/game'
import * as helper from '../helpers/index'
import { PagedData, buildPagedData } from '@/types/models/pagination'
import { NotFoundError } from '@/utils/ApiError'

const myGameRepository = () => AppDataSource.getRepository(TblGames)
const myGameFileRepository = () => AppDataSource.getRepository(TblGameFiles)
const myGiftsRepository = () => AppDataSource.getRepository(TblGifts)
const myUserEventGiftsRepository = () => AppDataSource.getRepository(TblUserEventGift)
const myGameLogRepository = () => AppDataSource.getRepository(TblGameLog)

export const getList = async (req: AuthorizedUserRequest): Promise<PagedData<any>> => {
  const { user } = req.authorizedUser

  const { limit, offset, order_by, order_type, page_number } = helper.sortAndPaging(req.query)

  const query = myGameRepository().createQueryBuilder('games').where({ active: 1 })

  const [data, count] = await query.skip(offset).take(limit).orderBy('games.createdAt', 'DESC').getManyAndCount()

  const results = data.map((game) => {
    return {
      id: game.id,
      slug: game.slug,
      thumb: game.thumb,
      name: game.name,
      preview_image: game.previewImage
    }
  })

  return buildPagedData<any>(results, count, { number: page_number, size: limit })
}

export const infoGame = async (request: GameRequest) => {
  const { user } = request.authorizedUser
  const id: number = parseInt(request.params['id'] || '1')

  const game = await myGameRepository().createQueryBuilder('games').where({ id: id }).getOne()
  if (game && game.active == 1) {
    const gameFiles = await myGameFileRepository().createQueryBuilder('game_files').where({ gameId: game.id }).getMany()

    let html = ''
    let css = ''
    let js = ''
    const config = JSON.parse(game.configFields)
    const requiredJs = []

    for (let i = 0; i < gameFiles.length; i++) {
      const file = gameFiles[i]
      if (file.fileType == 0) {
        html = file.content
      } else if (file.fileType == 1) {
        css = file.filePath
      } else if (file.fileType == 2 && file.fileName == 'game.js') {
        js = file.filePath
      } else if (file.fileType == 2 && file.fileName != 'config.js') {
        requiredJs.push(file.filePath)
      }
    }

    return {
      id: game.id,
      image: game.image,
      image_list: game.image_list ? JSON.parse(game.image_list) : [],
      name: game.name,
      slug: game.slug,
      preview_image: game.previewImage,
      config: config,
      external: game.external,
      video: game.video,
      html: html,
      css: css,
      js: js,
      width: 306,
      height: 617,
      required_js: requiredJs,
      full_desc: game.full_desc,
      created_at: game.createdAt
    }
  } else throw new NotFoundError(`Game slug=${'slug'} không tồn tại.`, '', 1)
}

/**
 * {
        "image": "https://gamecenter-kamgift.mediacdn.vn/1745028966337.png",
        "name": "Safe Area",
        "probability": 0.05,
        "background_color": "#FFEFE9",
        "quantity": 10,
        "type" :0
      },
 * @param request 
 * @returns 
 */
export const listGifts = async (request: GameRequest) => {
  const id: number = parseInt(request.params['id'] || '1')

  const gifts = await myGiftsRepository().createQueryBuilder('gifts').where({ eventId: id }).getMany()

  const results = gifts.map((gift) => {
    return {
      id: gift.id,
      name: gift.name,
      total: gift.total,
      remain: gift.remain,
      image: gift.image
    }
  })
  return results
}

function getType0(gifts) {
  let index = -1
  for (let i = 0; i < gifts.length; i++) {
    if (gifts[i].type == 0) {
      index = i
      break
    }
  }

  return index + 1
}
/**
 * user_event_gift
 * tim so gift
 * @param request
 */
export const reward = async (request: GameRequest) => {
  const { user } = request.authorizedUser
  const id: number = parseInt(request.params['id'] || '1')

  const gifts = await myGiftsRepository().createQueryBuilder('gifts').where({ eventId: id }).getMany()

  const availableSegments = gifts.filter((s) => s.remain > 0)

  if (availableSegments.length === 0) return gifts[getType0(gifts)] // Không còn segment nào để chọn

  // Tính tổng xác suất của các segment còn quantity
  const totalProbability = availableSegments.reduce((sum, seg) => sum + seg.probability / 100, 0)

  // Sinh số ngẫu nhiên trong khoảng từ 0 đến tổng xác suất
  const rand = Math.random() * totalProbability

  let cumulative = 0
  for (let i = 0; i < availableSegments.length; i++) {
    cumulative += availableSegments[i].probability / 100
    if (rand <= cumulative) {
      // Tìm index thật trong mảng gốc
      const originalIndex = gifts.findIndex((s) => s === availableSegments[i])
      const gift = gifts[originalIndex + 1]
      const giftData = await myGiftsRepository().createQueryBuilder('gifts').where({ id: gift.id }).getOne()

      giftData.remain--

      await myGiftsRepository().save(giftData)

      //ghi log qua neu nhu la qua
      if (gift.type) {
        const gameLog = await myGameLogRepository().createQueryBuilder('game_logs').where({ game_id: id }).getOne()

        gameLog.giftGiven++
        gameLog.giftRemain--

        await myGameLogRepository().save(gameLog)
      }

      //add trung thuong cho user
      //luu kho qua
      await myUserEventGiftsRepository().save({
        userId: user.id,
        eventId: id,
        giftId: gift.id
      })

      // Trả về index + 1 hoặc segment tùy theo yêu cầu
      return gifts[originalIndex + 1]
    }
  }
}

/**
 * danh sach trung thuong
 * @param request
 */
export const winners = async (request: GameRequest) => {
  const id: number = parseInt(request.params['id'] || '1')

  const results = await myUserEventGiftsRepository()
    .createQueryBuilder('user_event_gifts')
    .leftJoinAndSelect('user_event_gifts.user', 'user')
    .leftJoinAndSelect('user_event_gifts.event', 'event')
    .leftJoinAndSelect('user_event_gifts.gift', 'gift')
    .where({ eventId: id })
    .getMany()

  const data = results.map((result) => {
    return {
      gift_name: result.gift.name,
      user_name: result.user.name,
      gift_price: result.gift.price
    }
  })
  return data
}

/**
 * qua tang cua toi
 * @param request
 * @returns
 */
export const myGifts = async (request: GameRequest) => {
  const { user } = request.authorizedUser
  const id: number = parseInt(request.params['id'] || '1')

  const results = await myUserEventGiftsRepository()
    .createQueryBuilder('user_event_gifts')
    .leftJoinAndSelect('user_event_gifts.user', 'user')
    .leftJoinAndSelect('user_event_gifts.event', 'event')
    .leftJoinAndSelect('user_event_gifts.gift', 'gift')
    .where({ eventId: id, userId: user.id })
    .getMany()
  const gifts = []
  for (let i = 0; i < results.length; i++) {
    if (results[i].gift.type)
      gifts.push({
        name: results[i].gift.name,
        price: results[i].gift.price,
        image: results[i].gift.image
      })
  }

  return gifts
}

export const gameLogs = async (request: GameRequest) => {
  const id: number = parseInt(request.params['id'] || '1')

  const gameLog = await myGameLogRepository().createQueryBuilder('game_logs').where({ gameId: id }).getOne()

  return {
    gift_remain: gameLog.giftRemain,
    gift_given: gameLog.giftGiven,
    voucher_given: gameLog.voucherGiven,
    plays: gameLog.plays
  }
}


