import express, { <PERSON><PERSON> } from 'express'
import * as path from 'path'
//import formidable from 'formidable'
import * as formidable from 'formidable'
import * as fs from 'async-file'
import * as isImage from 'is-image'
import { MySqlCnn } from '@/config/config'
import axios from 'axios'
import { VOUCHER_HEADER_TOKEN, API_UPLOAD_AVATAR } from '@/config/constant'
import request from 'request'
import FormData from 'form-data'
//import { TblZalo<PERSON>uthens } from '@/entities/TblZaloAuthen'
import { TblZaloAuthens, TblSmsLog } from '@/entities'
import { AppDataSource } from '@/config/config'
import './../globals';
import crypto from 'crypto';

const zaloRepository = () => AppDataSource.getRepository(TblZaloAuthens);
const smsRepository = () => AppDataSource.getRepository(TblSmsLog);

async function uploadImageToStorage(imageUrl) {
  return new Promise((resolve) => {
    const endpoint = API_UPLOAD_AVATAR
    // Assume `imageName` is defined somewhere in your code
    // const images = imageUrl.split('/')
    // const date = new Date()
    // const fileName = Math.floor(date.getTime() / 1000) + images[images.length - 1]

    // Create the data object
    const data = {
      "url": imageUrl
    }

    // Send the request using axios
    axios
      .post(endpoint, data, {
        headers: {
          'Content-Type': 'multipart/form-data',
        }
      })
      .then((response) => {
        console.log(response.data);

        return resolve(response.data)
      })
      .catch((error) => {
        //console.error(error);

        console.log('fail:', error);

        return resolve(error)
      })
  })
}

export const uploadAvatar = async (req, res) => {
  return new Promise((resolve) =>{
    const form = new formidable.IncomingForm(),
    files = [],
    fields = [];

    form.uploadDir = path.join(__dirname, '../../images/');

    form.on('field', function (field, value) {
      //console.log(`This is field[${field}]: ${value}`)

      fields.push([field, value])
    })

    form.on('file', function (field, file) {
      //console.log(`This is filename: ${file}`)

      files.push(file)
    })

    form.on('end', async () => {    
      //const imgs = []
      let url = "";

      for (let i = 0; i < files.length; i++) {
        try{
          const file = files[i]
          const fileExt = path.extname(file.originalFilename)
          const fileName = new Date().getTime().toString() + fileExt
          const filePath = path.join(form.uploadDir, fileName)

          await fs.rename(file.filepath, filePath);

          url = req.protocol + "://" + req.get('Host') + "/images/" + fileName;

          let objTmp = null;
          objTmp = await uploadImageToStorage(url);
          
          if(objTmp.status == true){
            url = objTmp.filePath;
          }
        }
        catch(ex)
        {
          console.log(ex);

          return resolve({
            "status": 0,
            "message": ex
          });
        }
        
        //imgs.push('images/' + fileName);
      }

      return resolve({
        "status": 1,
        "url": url
      });      
    })

    form.parse(req)    
  });
}

export const mysqlQueryData = async (sql) => {
  const connection = MySqlCnn()

  return new Promise((resolve) => {
    //Run the query
    connection.query(sql, function (error, results) {
      if (error) {
        throw error
      }

      connection.destroy()

      return resolve(results)
    })
  })
}

export const voucherPost = async (endPoint, value) => {
  return new Promise((resolve) => {
    axios
      .post(endPoint, value, {
        headers: {
          'cb-2fa-token': VOUCHER_HEADER_TOKEN
        }
      })
      .then((response) => {
        resolve(response.data)
      })
      .catch((error) => {
        resolve(null)
      })
  })
}

async function callApi(url, data = {}, headers = {}) {
  return new Promise((resolve) => {
    axios
      .post(url, data, {
        headers: headers
      })
      .then((response) => {
        resolve(response.data)
      })
      .catch((error) => {
        console.log(`callApi erro: ${error}`);
        
        resolve(null)
      })
  });
}

async function callZaloApi(phone, otp, url, templateId, accessToken){
  return new Promise((resolve) => {
    const formData = {
        phone: '84' + phone.slice(1),
        template_id: templateId,
        template_data: {
            otp: otp,
            otp_expire: 5 * 60 / 60 //60p het han
        }
    };

    const config = {
      method: 'post',
      maxBodyLength: Infinity,
      url: url,
      headers: { 
        'access_token': accessToken, 
        'Content-Type': 'application/json'
      },
      data : formData
    };

    axios.request(config)
    .then((response) => {
      //console.log(JSON.stringify(response.data));

      return resolve(response.data)
    })
    .catch((error) => {
      console.log(error);

      resolve(null)
    });
  });

  
}

//zalo helper
class ZaloAuthenInfo {
  // public static secretKey = "T5Z7rKoESuIlX2CWI3W9";

  // public static appId = "709169281144635078";

  // public static enableSmsZaloNotExisted = 1;

  // public static loginTemplateId = "441433";

  // public static refreshToken = "FmnmPE2kVm8q1NTivg9u03K1TcVzpNbiS2nnT-_A4HzR2qemXTav6mjpL3Qbj1i7UtDnDlwAEWjmUraXvSKFE5jXHH_bnoiEK6TeCuUy15WO6X0ohE4QFG4LRrgowHye6Iv58gdwIGKw9o8ycVDZDqPKBopLfI9BKLbsKD6k1KbeTL5cmgS7S4O0RH_LrGSfM79PBjAE9qW3Q2b0tA4XHarZQcxSn25_InvDGPx3CbaBQN91XealMHbu86I9ccrnLriwT_B-RKTN9Jz8o_X5M4ufAZx7boDOGcrTSkk9K5LvN5jv-8uMHJCbNL-yubmoTniE9QUwPo9rSGiiffK5C7byKotdbn0ZDqvX1BMNOIm5OGqQiATOV1zj2c6ncW5CRtuEKQISKKCiFXjblPvkJpK_4KH7vfCvN-snVWi";
  
  // public static accessToken = "2n3tO37WOty06V1eD-naVG4TyMCiao8FQs3aG0xF238vHFnjTy8J4JTGm0vRurOG3nhPHssOJ2i12DXxQBvF6LqxiWSIit9994Aq9nRvP6jtQBnq79ej07qOo50AjmuJFZh730wvLKuWIwuWHUHMPnvsc15vyaW0BtZVSHhC40T8M_57Sw8Y1JeeuHC9i350MKc-23d5RMzELOSQF_b_UrTyy2S4-MH3TdUsFG_ONsb7JB8V2RraVtqiycbFcc1-0MEHANBaHra9RjP8PzuiEI5zzdLJZmia2XhPGqkj25G23_yRQ9eeN0eil4W-tsOC3slyPo672IPjBy0vDeyoR6S1w2KYcWD6TJ7-KH2NSKLI7hOJ98XQ0bKaatnXZMGzEtwIB7doULeSQwmEK-T2H4SnLmF1eYOy_s9s";
  
  // public static timeRefreshToken = 20 * 60 * 60; //20h

  // public static lastTimeDoRefreshToken = 0;

  // public static isReloadAuthenInfo = 0;

  public static async initAuthenInfo(){
    try{
      let reloadToken = 0;
      //let zaloInfo = null;
      const currTime = Math.round(+new Date() / 1000);

      if(global.ZaloAuthenInfo.lastTimeDoRefreshToken){
        if(currTime - global.ZaloAuthenInfo.lastTimeDoRefreshToken >= global.ZaloAuthenInfo.timeRefreshToken){
          reloadToken = 1;
        }
      }
      else{
        reloadToken = 1;
      }

      if(global.ZaloAuthenInfo.isReloadAuthenInfo){
        reloadToken = 1;

        global.ZaloAuthenInfo.isReloadAuthenInfo = 0;
      }

      if(reloadToken == 1){
        const zaloDbInfo = await zaloRepository().findOne({ where: { id: 2 } });

        if(zaloDbInfo){
          global.ZaloAuthenInfo.timeRefreshToken = zaloDbInfo.timeRefreshToken;
          global.ZaloAuthenInfo.lastTimeDoRefreshToken = zaloDbInfo.lastTimeDoRefreshToken;
          global.ZaloAuthenInfo.accessToken = zaloDbInfo.accessToken;
          global.ZaloAuthenInfo.refreshToken = zaloDbInfo.refreshToken;
          global.ZaloAuthenInfo.secretKey = zaloDbInfo.secretKey;
          global.ZaloAuthenInfo.appId = zaloDbInfo.appId;
          global.ZaloAuthenInfo.enableSmsZaloNotExisted = zaloDbInfo.enableSmsZaloNotExisted;
          global.ZaloAuthenInfo.loginTemplateId = zaloDbInfo.loginTemplateId;
          global.ZaloAuthenInfo.reloadToken = zaloDbInfo.reloadToken;

          // console.log(`${currTime} - ${global.ZaloAuthenInfo.lastTimeDoRefreshToken} < ${global.ZaloAuthenInfo.timeRefreshToken}, ${reloadToken}`);
          // console.log(`zaloDbInfo: ${JSON.stringify(zaloDbInfo)}`);

          if(global.ZaloAuthenInfo.lastTimeDoRefreshToken){
            if(currTime - global.ZaloAuthenInfo.lastTimeDoRefreshToken >= global.ZaloAuthenInfo.timeRefreshToken){
              global.ZaloAuthenInfo.lastTimeDoRefreshToken = currTime;
              reloadToken = 1;
            }
            else{
              reloadToken = 0;
            }
          }
          else{
            reloadToken = 0;
          }
        }
        else{
          global.ZaloAuthenInfo.accessToken = "";
          global.ZaloAuthenInfo.refreshToken = "";

          return;
        }

        //ff
        if(reloadToken == 1 && global.ZaloAuthenInfo.reloadToken){
          const result = await ZaloAuthenInfo.createZaloTokenByRFToken();

          if(zaloDbInfo && result){
            if(result.error_code == 0 && global.ZaloAuthenInfo.accessToken){
              zaloDbInfo.lastTimeDoRefreshToken = global.ZaloAuthenInfo.lastTimeDoRefreshToken;
              zaloDbInfo.accessToken = global.ZaloAuthenInfo.accessToken;
              zaloDbInfo.refreshToken = global.ZaloAuthenInfo.refreshToken;

              await zaloRepository().save(zaloDbInfo);
            }
          }
        }
      }      
    }
    catch(ex){
      console.log(`initAuthenInfo err: ${ex.message}`);
    }    
  }

  private static async createZaloTokenByRFToken() {
    const result = {
        status: 1,
        error_code: 0,
        message: "",
        data: {
          apiUrl: "",
          "brand": "vccorp",
          form_data: {},
          response: {
            data: {}
          }
        }
    };

    if(global.ZaloAuthenInfo.refreshToken == "" || global.ZaloAuthenInfo.accessToken == ""){
      result.error_code = 111;
      result.message = "refresh_token empty";

      return result;
    }

    try {
      const secretkey = global.ZaloAuthenInfo.secretKey;
      const appId = global.ZaloAuthenInfo.appId;
      const refeshToken = global.ZaloAuthenInfo.refreshToken;

      const apiUrl = 'https://oauth.zaloapp.com/v4/oa/access_token';
      const headers = {
          'Content-Type': 'application/x-www-form-urlencoded',
          'secret_key': secretkey
      };

      const formData = new URLSearchParams();
      formData.append('app_id', appId);
      formData.append('grant_type', 'refresh_token');
      formData.append('refresh_token', refeshToken);

      //declare for log
      const smsLog = new TblSmsLog();
      const param = {
        apiUrl,
        headers,
        formData
      }

      smsLog.phone = '1';
      smsLog.type = 4;
      smsLog.status = 0;
      smsLog.param = JSON.stringify(param);
        
      let response = null;
      response = await callApi(apiUrl, formData, headers);

      smsLog.status = 1;
      smsLog.result = JSON.stringify(response);

      await smsRepository().save(smsLog);
    

      const res = response.data? response.data : response;

      result.data.apiUrl = apiUrl;
      result.data.form_data = Object.fromEntries(formData.entries());
      result.data.response = res;

      if (!res) {
          result.error_code = 6;

          return result;
          //throw new Error('Máy chủ zalo không phản hồi!');
      }

      if (!(res.access_token && res.refresh_token)) {
          result.error_code = 5;
          
          return result;
          //throw new Error('Lỗi không có thông tin access_token và refresh_token!');

      }

      global.ZaloAuthenInfo.accessToken = res.access_token;
      global.ZaloAuthenInfo.refreshToken = res.refresh_token;
      global.ZaloAuthenInfo.lastTimeDoRefreshToken = Math.round(+new Date() / 1000);  
  
      return result;

    } catch (e) {
        result.status = 0;
        result.error_code = 6;
        result.message = e.message;
                
        return result;
    }
  }
}

class ZaloService {
  public static async sendZaloOTP(phone, otp, brand = 'vccorp', type = 'otp') {
    const result = {
        status: 1,
        error_code: 0,
        message: '',
        data: {}
    };

    if(global.ZaloAuthenInfo.refreshToken == "" || global.ZaloAuthenInfo.accessToken == ""){
      result.error_code = 111;
      result.message = "refresh_token empty";

      return result;
    }

    //const otp = Math.floor(100000 + Math.random() * 900000).toString();

    const formData = {
        phone: '84' + phone.slice(1),
        template_id: global.ZaloAuthenInfo.loginTemplateId,
        template_data: {
            otp: otp,
            otp_expire: 5 * 60 / 60 //60p het han
        }
    };

    return new Promise(async (resolve) => {
      try {
        if (!global.ZaloAuthenInfo.accessToken) throw new Error('Không tìm thấy cấu hình Zalo');

        const smsLog = new TblSmsLog();
        const param = {
          "zaloToken": global.ZaloAuthenInfo.accessToken,
          "headers": {
              'access_token': global.ZaloAuthenInfo.accessToken,
              'Content-Type': 'application/json'
          },
          "formData": formData
        };

        smsLog.phone = phone;
        smsLog.type = 1;
        smsLog.status = 0;
        smsLog.param = JSON.stringify(param);
        const apiUrl = 'https://business.openapi.zalo.me/message/template';        

        let response = null;
        response = await callZaloApi(phone, otp, apiUrl, global.ZaloAuthenInfo.loginTemplateId, global.ZaloAuthenInfo.accessToken);

        const resData = response;

        smsLog.status = 1;
        smsLog.result = JSON.stringify(response);

        await smsRepository().save(smsLog);

        if (!resData) {
            result.error_code = 6;
            result.message = "Máy chủ zalo không phản hồi!";

            return resolve(result);
        }

        result.data = {
            apiUrl,
            form_data: formData,
            response: resData,
            send_otp: 'zalo'
        };

        if (resData.error !== undefined && resData.message !== undefined) {
            if (resData.error === 0) {
                result.message = resData.message;
            } else if ([ -100, -108, -118, -119, -124, -133, -139, -141 ].includes(resData.error)) {
                // Zalo account not existed
                if (type === 'otp' && global.ZaloAuthenInfo.enableSmsZaloNotExisted == 1) {
                    result.status = 0;
                    result.error_code = 10;
                    result.message = resData.error;

                    return resolve(result);
                } else {
                    result.error_code = 5;

                    switch (resData.error) {
                        case -108:
                            result.message = 'Số điện thoại không hợp lệ.'; break;
                        case -118:
                            result.message = 'Số điện thoại không tồn tại hoặc đã bị vô hiệu hoá.'; break;
                        case -119:
                            result.message = 'Số điện thoại không thể nhận thông báo.'; break;
                        case -133:
                            result.message = 'Zalo không hỗ trợ gửi mã OTP vào ban đêm (từ 22h-6h).'; break;
                        case -139:
                            result.message = 'Số điện thoại của bạn đang từ chối nhận thông báo OTP này trên Zalo.'; break;
                        case -141:
                            result.message = `Số điện thoại của bạn đang từ chối nhận mã OTP từ Official Account ${brand}`; break;
                        default:
                            result.message = resData.message || 'Lỗi không xác định từ Zalo';
                    }
                    if ([ -108, -118, -119, -139 ].includes(resData.error)) {
                        result.message += ' Vui lòng nhập số điện thoại zalo khác để nhận mã OTP.';
                    }

                    return resolve(result);
                }
            } else {
                result.status = 0;
                result.error_code = 12;
                result.message = resData.message;

                return resolve(result);
            }
        } else {
            result.error_code = 5;
            result.message = 'Cấu trúc dữ liệu zalo trả ra không hợp lệ!';

            return resolve(result);
        }

        return resolve(result);
      } catch (e) {
          result.status = 0;
          result.error_code = result.error_code === 0 ? -1 : result.error_code;
          result.message = e.message;
          
          return resolve(result);
      }
    });
  }
}

class CommonService {
  public static createHmacWithCurrentTime(bizflyProjectToken: string, apiSecret: string, timeNow: number) {
    const message = timeNow + bizflyProjectToken;
    const hmac = crypto.createHmac('sha512', apiSecret);
    hmac.update(message);
    return hmac.digest('hex');
  };

  public static getCurrentTime() {
    return Date.now();
  }
}

export { ZaloAuthenInfo, ZaloService, CommonService }