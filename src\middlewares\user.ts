import { UserRole } from '@/entities/TblUsers'
import { RequestHandler, Response } from 'express'
import { AuthorizedUserRequest } from './auth'
import ApiError from '@/utils/ApiError'

export interface UserPermission {
  role: UserRole
}

export const AdminManager = {
  role: UserRole.ADMIN
}

export const User = {
  role: UserRole.NORMAL
}

export const NormalUser = [User]
export const AdminUser = [AdminManager]
export const AllUser = [User, AdminManager]

export const checkUserPermission = (permissions: UserPermission[]): RequestHandler => {
  return (req: AuthorizedUserRequest, res: Response, next) => {
    const user = req.authorizedUser.user
    for (const i in permissions) {
      const permission = permissions[i]
      if (user.roleId === permission.role) {
        return next()
      }
    }
    next(new ApiError(200, 'E403', 'Permission denied'))
  }
}
