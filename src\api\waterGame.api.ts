import express from 'express'
import waterGameService from '@/services/waterGame.service'
import { AuthorizedUserRequest } from '@/middlewares/auth'
import { DrawGiftRequest } from '@/types/requests/game'
// import { Request, Response } from 'express'

const loadWaterGameHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {
    const { user } = req.authorizedUser
    const { campaignId } = req.body
    if (!campaignId) {
      return res.status(200).json({ status: 200, error_code: 1, message: 'Missing campaignId' })
    }
    const gameInfo = await waterGameService.getGameByCampaignId(campaignId)
    if (!gameInfo) throw new Error('Game not found')
    const gameId = gameInfo.id;
    const userGift = await waterGameService.getCurrentUserGift(user.id, gameId)
    const listConfig = await waterGameService.getAllConfigs(gameId)

    let config = null
    let maxDay = 0
    if (userGift) {
      config = await waterGameService.getConfigById(userGift.currWaterGiftConfigId)
      maxDay = await waterGameService.getMaxDay(user.id)
    }
    return res.status(200).json({
      status: 200,
      error_code: 0,
      data: {
        user_gift: userGift || null,
        day: config?.day || null,
        updated_at: userGift ? new Date(userGift.updatedAt).getTime() : null,
        config: config,
        listConfig: listConfig,
        maxDay: maxDay,
        gameInfo: gameInfo
      }
    })
  } catch (e) {
    return res.status(200).json({ status: 200, error_code: 1, message: e.message })
  }
}

const waterHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {
    const { day, gift, gameId } = req.body
    const { user } = req.authorizedUser
    if (!gameId) {
      return res.status(200).json({ status: 200, error_code: 1, message: 'Missing gameId' })
    }
    const result = await waterGameService.water(user.id, day, gift, gameId)
    return res.status(200).json({ status: 200, error_code: 0, data: { user_gift: result } })
  } catch (e) {
    if (e.code === 'ALREADY_WATERED_TODAY') {
      return res.status(200).json({ status: 200, error_code: 2, message: 'Ngày hôm nay bạn đã tưới rồi' })
    }
    return res.status(200).json({ status: 200, error_code: 1, message: e.message })
  }
}

const getUserRewardCodesHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {
    const { user } = req.authorizedUser
    const { campaignId } = req.query
    if (!campaignId) {
      return res.status(200).json({ status: 200, error_code: 1, message: 'Missing campaignId' })
    }
    const rewardCodes = await waterGameService.getUserRewardCodes(user.id, parseInt(campaignId as string) || 0)
    return res.status(200).json({ 
      status: 200, 
      error_code: 0, 
      data: { reward_codes: rewardCodes } 
    })
  } catch (e) {
    return res.status(200).json({ status: 200, error_code: 1, message: e.message })
  }
}

const getTodayStatsHandler = async (req: express.Request, res: express.Response) => {
  try {
    const { gameId } = req.query
    if (!gameId) {
      return res.status(200).json({ status: 200, error_code: 1, message: 'Missing gameId' })
    }
    const totalUserWatering = await waterGameService.getTotalUserWatering(parseInt(gameId as string) || 0)
    const totalRewardCodes = await waterGameService.getTotalRewardCodesToday(parseInt(gameId as string) || 0)
    return res.status(200).json({
      status: 200,
      error_code: 0,
      data: {
        total_watering: totalUserWatering + 3600,
        total_reward_codes: totalRewardCodes
      }
    })
  } catch (e) {
    return res.status(200).json({ status: 200, error_code: 1, message: e.message })
  }
}

// const createFromLogsHandler = async (req: Request, res: Response) => {
//   try {
//     const results = await waterGameService.createFromLogs()
//     return res.json({
//       success: true,
//       data: results
//     })
//   } catch (error: any) {
//     return res.status(500).json({
//       success: false,
//       message: error.message
//     })
//   }
// }

const drawGiftHandler = async (req: DrawGiftRequest, res: express.Response) => {
  try {
    const { campaignId, prizeId } = req.body
    const { user } = req.authorizedUser
    
    if (!campaignId || !prizeId) {
      return res.status(200).json({ 
        status: 200, 
        error_code: 1, 
        message: 'Missing campaignId or prizeId' 
      })
    }

    const result = await waterGameService.drawGift(campaignId, prizeId, user.id)
    
    return res.status(200).json({
      status: 200,
      error_code: 0,
      data: result,
      message: result.message
    })
  } catch (e) {
    return res.status(200).json({ 
      status: 200, 
      error_code: 1, 
      message: e.message 
    })
  }
}

export {
  loadWaterGameHandler,
  waterHandler,
  getUserRewardCodesHandler,
  getTodayStatsHandler,
  drawGiftHandler,
  // createFromLogsHandler
} 