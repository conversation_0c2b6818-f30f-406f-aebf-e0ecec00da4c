import { UserData } from '@/types/responses/auth'
import { AuthorizedUser } from '@/types/models/auth'
import { AppDataSource } from '@/config/config'
import { TblUsers } from '@/entities'
import ApiError from '@/utils/ApiError'
import httpStatus from 'http-status'
import logger from '@/config/logger'
import { jwtService } from '.'
import bcrypt from 'bcryptjs'
import { UserStatus } from '@/entities/TblUsers'
import express from 'express'
import { TokenType } from '@/types/models/token'
import { JsonWebTokenError, NotBeforeError, TokenExpiredError } from 'jsonwebtoken'
import { USER_ONLINE_ROLE_ID, USER_VERIFIED, MAIL_HOST, MAIL_POST, MAIL_SECURE, MAIL_USER, MAIL_PASS, USER_NO_VERIFY, ACTIVATE_EMAIL_LINK} from '@/config/constant'
import nodemailer from 'nodemailer';
import axios from 'axios';

const userRepository = () => AppDataSource.getRepository(TblUsers);

export interface AuthorizedUserRequest extends express.Request {
  authorizedUser: AuthorizedUser
}

const getToken = (req) => {
  if (req.headers.authorization) {
    const authorization = req.headers.authorization.split(' ');

    if (authorization.length === 2 && authorization[0] === 'Bearer') {
      return authorization[1]
    }
  }
  return null
}

const authenticateByUid = async (userId: number): Promise<AuthorizedUser> => {
  // 1. find user
  // const user = await userRepository().findOne({ where: { id: userId, deletedAt: IsNull() } })
  const user = await userRepository().createQueryBuilder('user').where({ id: userId }).getOne()
  
  if (!user) {
    // User authentication failure_user not found
    throw new ApiError(200, 'EUSER404', 'User not found')
  }

  // check active account
  if (user.active !== UserStatus.ACTIVE) {
    // user account is not active
    throw new ApiError(200, 'E102', 'User account is not active')
  }

  return { user }
}

export default {
  async login(email, password, phone) {
    //password is stored in db with the hash bellow:
    const passwordT = "123456Aa@" //==> blank text

    const hashedPasswordT = await bcrypt.hash(passwordT, 8);
    console.log(`pass: ${hashedPasswordT}`);

    try{
      if (!email && !phone) {
        //throw new ApiError(httpStatus.BAD_REQUEST, 'E404', 'User not found')
        return ({status: 200, code: "EAUTH200", "error_code":"211", message: 'Vui lòng nhập email hoặc điện thoại'});
      }

      let user = null;
      
      if(email){
        user = await userRepository().findOne({ where: { email: email } });
      }
      else if(phone){
        user = await userRepository().findOne({ where: { phone: phone } });
      }

      if (!user) {
        //throw new ApiError(httpStatus.BAD_REQUEST, 'E404', 'User not found')
        return ({status: 200, code: "EAUTH200", "error_code":"212", message: 'User not found'});
      }
      
      if (user.active == UserStatus.USER_NO_VERIFY) {
        // user account is not active
        //throw new ApiError(200, 'E102', 'User account is not active')
        return ({status: 200, code: "E102", "error_code":"213", message: 'Tài khoản chưa được xác minh, vui lòng đăng ký lại'});
      }else if (user.active !== UserStatus.ACTIVE) {
        // user account is not active
        //throw new ApiError(200, 'E102', 'User account is not active')
        return ({status: 200, code: "E102", "error_code":"217", message: 'User account is not active'});
      }

      if((user.password == null || user.password == "") && user.googleId && email){
        return ({status: 200, code: "E102", "error_code":"218", message: 'Bạn đăng nhập tài khoản này bằng Google'});
      }

      const hashedPass = user.password;
      const isMatch = await bcrypt.compare(password, hashedPass);

      if(!isMatch){
        //throw new ApiError(httpStatus.BAD_REQUEST, 'E404', 'User not found')
        return ({status: 200, code: "E404", "error_code":"215", message: 'User name or password is not correct'});
      }

      const ACCESS_TOKEN = jwtService.generateAccessToken(user.id, user.roleId);
      const REFRESH_TOKEN = jwtService.generateRefreshToken(user.id, user.roleId)

      user.isLoging = 1;
      user.accessToken = ACCESS_TOKEN;
      user.refreshToken = REFRESH_TOKEN;

      await userRepository().save(user);

      return {
        "status": 200,
        "error_code": 0,
        "message": "Đăng nhập thành công",
        "data": {
            "access_token": ACCESS_TOKEN,
            "refreshToken": REFRESH_TOKEN,
            "token_type": "Bearer",
            user: {
              id: user.id,
              name: user.name,
              email: user.email,
              phone: user.phone,
              roleId: user.roleId,
              active: user.active,
              avatar: user.avatar,
              accessToken: user.accessToken,
              is_update_info: user.isUpdateInfo,
              contact_phone : user.contactPhone,
              contact_phone_authened: user.contactPhoneAuthened
            } as UserData
        } 
      }
    }
    catch(e){
      return ({status: 200, code: "EAUTH200", "error_code":"210", message: e});
    }
  },

  async loginByTokioLife(user_id, tokyoId: string){
    if(!user_id){
       return ({status: 200, code: "EAUTH200", "error_code":"211", message: 'Thiếu userID'});
    }

    const user =  await userRepository().findOne({ where: { id: user_id, tokyoId: tokyoId } });

    if (!user) {
        //throw new ApiError(httpStatus.BAD_REQUEST, 'E404', 'User not found')
        return ({status: 200, code: "EAUTH200", "error_code":"212", message: 'User not found'});
    }

    const ACCESS_TOKEN = jwtService.generateAccessToken(user.id, user.roleId);
    const REFRESH_TOKEN = jwtService.generateRefreshToken(user.id, user.roleId)

    user.isLoging = 1;
    user.accessToken = ACCESS_TOKEN;
    user.refreshToken = REFRESH_TOKEN;

    await userRepository().save(user);

    return {
      "status": 200,
      "error_code": 0,
      "message": "Đăng nhập thành công",
      "data": {
          "access_token": ACCESS_TOKEN,
          "refreshToken": REFRESH_TOKEN,
          "token_type": "Bearer",
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            phone: user.phone,
            roleId: user.roleId,
            active: user.active,
            avatar: user.avatar,
            accessToken: user.accessToken,
            is_update_info: user.isUpdateInfo,
            contact_phone : user.contactPhone,
            contact_phone_authened: user.contactPhoneAuthened
          } as UserData
      } 
    }
  },

  async loginViaSocial(inUser, type) {
    try{      
      const email = inUser.emails[0].value;
      let user = await userRepository().findOne({ where: { email: email } });

      if(user == null){      
        user = new TblUsers();
        user.email = email;
        user.active = USER_VERIFIED;
        user.roleId = USER_ONLINE_ROLE_ID;        
      }
      
      const ACCESS_TOKEN = jwtService.generateAccessToken(user.id, user.roleId);
      const REFRESH_TOKEN = jwtService.generateRefreshToken(user.id, user.roleId)

      user.isLoging = 1;
      user.accessToken = ACCESS_TOKEN;
      user.refreshToken = REFRESH_TOKEN;

      if(type == 'google'){
        user.googleId = inUser.id;
      }
      else if(type == 'facebook'){
        user.facebookId = inUser.id;
      }
      
      if(inUser.photos && Array.isArray(inUser.photos) && inUser.photos.length > 0){
        if(!(user.avatar)){
          user.avatar = inUser.photos[0].value;
        }          
      }

      const userResult = await userRepository().save(user);

      return {
        //"error_code": 0,
        "access_token": ACCESS_TOKEN,
        "refreshToken": REFRESH_TOKEN,
        "token_type": "Bearer",
          user: {
            id: userResult.id,
            name: userResult.name,
            email: userResult.email,
            roleId: userResult.roleId,
            active: userResult.active,
            avatar: userResult.avatar,
          } as UserData
        }
    }
    catch(e){
      return ({status: 200, code: "EAUTH200", "error_code":"210", message: e});
    }
  },

  async authenticate(req, res, next) {
    try {
      const token = getToken(req);

      if (token === null) {
        return ({status: 200, code: "EAUTH400", "error_code":"1", message: "Token empty"})
      }

      // token verify
      const payload = jwtService.verifyAuthenticationToken(token);

      if (!payload) {
        return ({status: 200, code: "EAUTH401", "error_code":"4", message: "Token error"})
      }

      if (payload.typ !== TokenType.AccessToken) {
        return ({status: 200, code: "EAUTH402", "error_code":"5", message: "Token type error"})
      }
      
      const authorizedUser = await authenticateByUid(payload.id);

      if(authorizedUser){
        return ({status: 200, code: "", "error_code":"", user: {
          id: authorizedUser.user.id,
          name: authorizedUser.user.name,
          roleId: authorizedUser.user.roleId,
          email: authorizedUser.user.email,
          avatar: authorizedUser.user.avatar
        }})
      }

      next();
    } catch (e) {
      switch (e.constructor) {
        case JsonWebTokenError:{
            return ({status: 200, code: "EAUTH402", "error_code":"1", message: "Token error"});            
        }
        case TokenExpiredError:;
          return ({status: 200,code: "EAUTH402", "error_code":"2", message: "Token expired"});
        case NotBeforeError:
          return ({status: 200,code: "EAUTH403", "error_code":"3", message: "Token not before"});
        default:
          next(e)
      }
    }
  },
  
  async sendOtpViaEmail(email, otp){
    return new Promise((resolve) =>{
      try{
        const transporter = nodemailer.createTransport({
            host: MAIL_HOST, // Thay bằng máy chủ SMTP của bạn
            port: MAIL_POST, // Cổng SMTP (thường là 465 cho SSL hoặc 587 cho TLS)
            secure: MAIL_SECURE, // true cho SSL, false cho các giao thức khác
            auth: {
              user: MAIL_USER, // Thay bằng email của bạn
              pass: MAIL_PASS   // Thay bằng mật khẩu email của bạn
            }
        });

        const r = (Math.random() + 1).toString(100).substring(70);
        
        // Tạo email chứa mã OTP
        let verifyLink = ACTIVATE_EMAIL_LINK.replace('[EMAIL]', email);
        verifyLink = verifyLink.replace('[OTP]', otp);
        verifyLink = verifyLink.replace('[KEY]', r);

        const mailOptions = {
          from: {
            name: 'Kamgift',
            address: MAIL_USER
          },
          to: email,
          subject: `KAM verify account`,
          html: `<span>Click <a href='${verifyLink}'>here</a> to activate your account</span>`
        };
  
        transporter.sendMail(mailOptions, async (error, info) => {
            if (error) {
                resolve({
                  status: 0,
                  mesage: error
                });
            }
  
            //const result = await commonService.mysqlQueryData(`call sent_otp('${email}', '${otp}')`);
            let user = await userRepository().findOne({ where: { email: email } });

            if(user == null){      
              user = new TblUsers();
              user.email = email;
              user.active = USER_NO_VERIFY;
            }

            user.remmemberToken = r;
            user.indentifyCode = otp;

            await userRepository().save(user);

            resolve({
              status: 1,
              mesage: {
                "email": user.email,
                "key": user.remmemberToken,
                "otp": user.indentifyCode
              }
            });
        });  
      }
      catch(ex){
        resolve({
          status: 0,
          mesage: ex
        });
      }
    });    
  },
  async verifyOtpViaEmail(email, otp, key){
    return new Promise(async (resolve) =>{
      try{
        const user = await userRepository().findOne({ where: { email: email } });
  
        if(user == null){ 
          return resolve({
            status: 0,
            message: "email not found"
          });
        }

        if(user.indentifyCode != otp){
          return resolve({
            status: 0,
            message: "Verify does not success"
          });
        }

        if(user.remmemberToken != key){
          return resolve({
            status: 0,
            message: "Verify does not success"
          });
        }

        user.remmemberToken = "";
        user.indentifyCode = "";
        user.active = 1;

        await userRepository().save(user);
        
        return resolve(user);
      }
      catch(ex){
        return resolve({
          status: 0,
          message: ex
        });
      }
    });    
  },

  async logout(email){
    const user = await userRepository().findOne({ where: { email: email } })

    if (!user) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'E404', 'User not found')
    }
    
    // check active account
    if (user.active !== UserStatus.ACTIVE) {
      // user account is not active
      throw new ApiError(200, 'E102', 'User account is not active')
    }

    // revoke token
    const accessToken = user.accessToken;
    //logger.info(accessToken)
    if (accessToken) {
      await jwtService.revokeAuthenticationToken(accessToken)
    }

    // // revoke refresh token
    const refreshToken = user.refreshToken;
    if (refreshToken) {
      await jwtService.revokeAuthenticationToken(refreshToken)
    }

    user.isLoging = 0;
    user.accessToken = "";
    user.refreshToken = "";

    await userRepository().save(user);
  },

  async me(authorizedUser: AuthorizedUser): Promise<UserData> {
    const user = authorizedUser.user

    return {
      id: user.id,
      name: user.name,
      email: user.email,
      roleId: user.roleId,
      active: user.active,
      avatar: user.avatar,
      // createdAt: user.createdAt,
      // updatedAt: user.updatedAt
    } as UserData
  }
}
