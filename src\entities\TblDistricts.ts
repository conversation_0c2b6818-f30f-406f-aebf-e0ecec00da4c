import { Column, Entity, PrimaryGeneratedColumn} from 'typeorm'
import { BaseEntity } from './base/BaseEntity'

@Entity('district', { schema: 'events' })
export class TblDistricts extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('varchar', { name: 'name', length: 255 })
  name: string

  @Column('varchar', { name: 'slug', length: 500 })
  slug: string

  @Column('int', { name: 'city_id', nullable: false })
  cityId: number

  //created_at

  //updated_at
}
