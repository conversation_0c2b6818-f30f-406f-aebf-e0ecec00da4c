import express from 'express'
import { articleLinkAPI } from '@/api'
import { authenticate, noAuthenticate } from '@/middlewares/auth'

const myArticleLinkRouter = express.Router()

myArticleLinkRouter.get('/guess/all', noAuthenticate, articleLinkAPI.articleLinkListNotAuthHandler)
myArticleLinkRouter.get('/', authenticate, articleLinkAPI.articleLinkListHandler)
myArticleLinkRouter.get('/:id/read', authenticate, articleLinkAPI.readArticleLinkHandler)
myArticleLinkRouter.get('/:id/extraSpin', authenticate, articleLinkAPI.extraSpinHandler)

// Export router
const router = express.Router()
router.use('/links', myArticleLinkRouter)

export default router

