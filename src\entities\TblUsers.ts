import { Column, Entity, Index, OneToMany, PrimaryGeneratedColumn } from 'typeorm'
import { TblFactorAuth } from './TblFactorAuth'
import { BaseEntity } from './base/BaseEntity'
import { TblUserArticleLink } from './TblUserArticleLink'

export enum UserRole {
  ADMIN = 2,
  NORMAL = 1,
  KOLS = 3
}

export enum UserStatus {
  ACTIVE = 1,
  INACTIVE = 0,
  USER_NO_VERIFY = 2,
  DELETED = 3
}

//@Index('email', ['email'], { unique: true })
@Entity('users', { schema: 'events' })
export class TblUsers extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('int', { name: 'role_id' })
  roleId: number

  @Column('varchar', { name: 'name', length: 255 })
  name: string

  @Column('varchar', { name: 'social', length: 255 })
  social: string

  @Column('varchar', {
    name: 'avatar',
    nullable: true,
    comment: 'avatar',
    length: 1024
  })
  avatar?: string | null

  // @Column('varchar', { name: 'email', unique: true, length: 255 })
  // email: string
  @Column('varchar', { name: 'email', length: 255 })
  email: string

  @Column('varchar', { name: 'password', length: 255 })
  password: string

  @Column('varchar', { name: 'phone', length: 255 })
  phone: string

  //position

  @Column('int', { name: 'active' })
  active: number

  @Column('int', { name: 'is_loging' })
  isLoging: number

  @Column('varchar', { name: 'indentify_code', length: 255 })
  indentifyCode: string

  @Column('varchar', { name: 'device_id', length: 50 })
  deviceId: string

  //description

  //remmember_token
  @Column('varchar', { name: 'remember_token', length: 255 })
  remmemberToken: string

  @Column('varchar', { name: 'access_token', length: 255 })
  accessToken: string

  @Column('varchar', { name: 'refresh_token', length: 255 })
  refreshToken: string

  //indentify_code_crc
  //....

  @Column('varchar', { name: 'facebook_id', unique: true, length: 255 })
  facebookId: string

  @Column('varchar', { name: 'google_id', unique: true, length: 255 })
  googleId: string

  @Column('varchar', { name: 'apple_id', unique: true, length: 255 })
  appleId: string

  @Column('varchar', { name: 'address', length: 2550 })
  address: string

  @Column('int', { name: 'city_id' })
  cityId: number

  @Column('int', { name: 'district_id' })
  districtId: number

  @Column('int', { name: 'commune_id' })
  communeId: number

  @Column('int', { name: 'external' })
  external: number

  @Column('int', { name: 'otp_sent_at' })
  otpSentAt: number

  @Column('int', { name: 'is_update_info' })
  isUpdateInfo: number
  
  @Column('varchar', { name: 'contact_phone', length: 255 })
  contactPhone: string

  @Column('int', { name: 'otp_contact_sent_at' })
  otpContactSentAt: number  

  @Column('int', { name: 'contact_phone_authened' })
  contactPhoneAuthened: number  

  @Column('varchar', { name: 'contact_phone_otp', length: 255 })
  contactPhoneOtp: string  

  @OneToMany(() => TblFactorAuth, (tblFactorAuth) => tblFactorAuth.user)
  tblFactorAuths: TblFactorAuth[]

  @OneToMany(() => TblUserArticleLink, (user) => user.articleLink)
  userArticleLinks: TblUserArticleLink[]

  @Column('varchar', {name: 'tokyo_id', length: 200})
  tokyoId: string

  @Column('varchar', {name: 'biz_id', length: 200})
  bizId: string

  @Column('int', { name: 'gender' })
  gender: number  

  @Column('date', { name: 'birthday' })
  birthday: string
}
