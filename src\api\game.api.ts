/* eslint-disable @typescript-eslint/naming-convention */
import express from 'express'
import { gameService } from '@/services'
import { GameRequest } from '@/types/requests/game'
import { AuthorizedUserRequest } from '@/middlewares/auth'
import { SuccessAPIResponse } from '@/types/responses/base'

const gameListHandler = async (req: AuthorizedUserRequest, res: express.Response, next: express.NextFunction) => {
  try {
    // list
    const data = await gameService.getList(req)
    // output
    const response:SuccessAPIResponse = {
      status: 200,
      code:"200",
      error_code: 0,
      message: 'success',
      data
    }
    res.status(200).json(response)
  } catch (e) {
    next(e)
  }
}

const infoGameHandler = async (req: GameRequest, res: express.Response, next: express.NextFunction) => {
  try {
    const data = await gameService.infoGame(req)
    // output
    const response:SuccessAPIResponse = {
      status: 200,
      code:"200",
      error_code: 0,
      message: 'success',
      data
    }
    res.status(200).json(response)
  } catch (e) {
    next(e)
  }
}

const listGiftHandler = async (req: GameRequest, res: express.Response, next: express.NextFunction) => {
  try {
    const data = await gameService.listGifts(req)
    // output
    const response:SuccessAPIResponse = {
      status: 200,
      code:"200",
      error_code: 0,
      message: 'success',
      data
    }
    res.status(200).json(response)
  } catch (e) {
    next(e)
  }
}

const rewardHandler = async (req: GameRequest, res: express.Response, next: express.NextFunction) => {
  try {
    const data = await gameService.reward(req)
    // output
    const response:SuccessAPIResponse = {
      status: 200,
      code:"200",
      error_code: 0,
      message: 'success',
      data
    }
    res.status(200).json(response)
  } catch (e) {
    next(e)
  }
}

const winnersHandler = async (req: GameRequest, res: express.Response, next: express.NextFunction) => {
  try {
    const data = await gameService.winners(req)
    // output
    const response:SuccessAPIResponse = {
      status: 200,
      code:"200",
      error_code: 0,
      message: 'success',
      data
    }
    res.status(200).json(response)
  } catch (e) {
    next(e)
  }
}

const myGiftsHandler = async (req: GameRequest, res: express.Response, next: express.NextFunction) => {
  try {
    const data = await gameService.myGifts(req)
    // output
    const response:SuccessAPIResponse = {
      status: 200,
      code:"200",
      error_code: 0,
      message: 'success',
      data
    }
    res.status(200).json(response)
  } catch (e) {
    next(e)
  }
}

const gameLogsHandler = async (req: GameRequest, res: express.Response, next: express.NextFunction) => {
  try {
    const data = await gameService.gameLogs(req)
    // output
    const response:SuccessAPIResponse = {
      status: 200,
      code:"200",
      error_code: 0,
      message: 'success',
      data
    }
    res.status(200).json(response)
  } catch (e) {
    next(e)
  }
}

export { gameListHandler, infoGameHandler, listGiftHandler, rewardHandler, winnersHandler, myGiftsHandler, gameLogsHandler}

