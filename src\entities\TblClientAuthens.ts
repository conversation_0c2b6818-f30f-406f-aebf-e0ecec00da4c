import { Column, Entity, PrimaryGeneratedColumn} from 'typeorm'
import { BaseEntity } from './base/BaseEntity'

@Entity('client_authens', { schema: 'events' })
export class TblClientAuthens extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('varchar', { name: 'client_id', length: 255 })
  clientId: string

  @Column('varchar', { name: 'client_secrect_key', length: 2000 })
  clientSecrectKey: string

  @Column('int', { name: 'status', nullable: false })
  status: number

  //created_at

  //updated_at
}
