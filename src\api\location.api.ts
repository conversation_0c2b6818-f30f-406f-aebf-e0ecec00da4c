/* eslint-disable @typescript-eslint/naming-convention */
import express from 'express'
import { apiLocation } from '@/services'

const getAllCity = async (req: express.Request, res: express.Response, next: express.NextFunction) => {
  try {
    const response = await apiLocation.getAllCity(req);

    res.status(200).json(response)
  } catch (e) {
    next(e)
  }
}

const listDistrictByCity = async (req: express.Request, res: express.Response, next: express.NextFunction) => {
  try {
    const response = await apiLocation.listDistrictByCity(req);

    res.status(200).json(response)
  } catch (e) {
    next(e)
  }
}

const lisCommuneByDistrict = async (req: express.Request, res: express.Response, next: express.NextFunction) => {
  try {
    const response = await apiLocation.lisCommuneByDistrict(req);

    res.status(200).json(response)
  } catch (e) {
    next(e)
  }
}

export { getAllCity, listDistrictByCity, lisCommuneByDistrict }
