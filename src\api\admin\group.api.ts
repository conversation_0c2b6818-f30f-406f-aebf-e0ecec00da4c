/* eslint-disable @typescript-eslint/naming-convention */
import express from 'express'
import { InputError } from '@/utils/ApiError'
import { requiredValidator } from '@/validators/validators'
import { body, validationResult } from 'express-validator'
import { CreateGroupRequest, UpdateGroupRequest, GroupRequest, UserGroupRequest } from '@/types/requests/group'
import { AuthorizedUserRequest } from '@/middlewares/auth'
import { BaseResponse } from '@/types/responses/base'

const createGroupInputValidator = [requiredValidator(body('name')), requiredValidator(body('description'))]

const editGroupInputValidator = [requiredValidator(body('name')), requiredValidator(body('description'))]

const createGroupHandler = async (req: CreateGroupRequest, res: express.Response, next: express.NextFunction) => {
  try {
    // validate input
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      throw new InputError('Input error')
    }


    // output
    const response: BaseResponse = {
      code: '0'
    }
    res.status(200).json(response)
  } catch (e) {
    next(e)
  }
}

const groupListHandler = async (req: AuthorizedUserRequest, res: express.Response, next: express.NextFunction) => {
  try {
    // list
    // output
    const response = {
      code: '0',
      
    }
    res.status(200).json(response)
  } catch (e) {
    next(e)
  }
}

const editGroupHandler = async (req: UpdateGroupRequest, res: express.Response, next: express.NextFunction) => {
  try {
    // validate input
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      throw new InputError('Input error')
    }
    // connect
    // output
    const response = {
      code: '0',
    }
    res.status(200).json(response)
  } catch (e) {
    next(e)
  }
}

const infoGroupHandler = async (req: GroupRequest, res: express.Response, next: express.NextFunction) => {
  try {
    // output
    const response = {
      code: '0',
    }
    res.status(200).json(response)
  } catch (e) {
    next(e)
  }
}

const deleteGroupHandler = async (req: GroupRequest, res: express.Response, next: express.NextFunction) => {
  try {
    // output
    const response: BaseResponse = {
      code: '0'
    }
    res.status(200).json(response)
  } catch (e) {
    next(e)
  }
}

export { createGroupInputValidator, editGroupInputValidator }
export { createGroupHandler, groupListHandler, editGroupHandler, deleteGroupHandler, infoGroupHandler }
