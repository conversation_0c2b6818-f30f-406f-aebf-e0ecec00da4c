import express from 'express'

export interface Page {
  number: number
  size: number
}

export interface PagedData<T> {
  content: T[]
  page_count: number
  item_count: number
  current_page: number
  page_size: number
}

export const emptyPagedData = (page: Page) => {
  return {
    content: [],
    page_count: 0,
    item_count: 0,
    current_page: page.number + 1,
    page_size: page.size
  }
}

export const buildPagedData = <T>(content: T[], count: number, page: Page) => {
  return {
    content,
    page_count: Math.ceil(count / page.size),
    item_count: count,
    current_page: page.number + 1,
    page_size: page.size
  }
}

export const pageFromQuery = (req: express.Request): Page => {
  const page = {
    number: Number(req.query.page_number) - 1 || 0,
    size: Number(req.query.page_size) || 20
  }
  if (page.number < 0) {
    page.number = 0
  }
  if (page.size < 1 || page.size > 200) {
    page.size = 20
  }
  return page
}
