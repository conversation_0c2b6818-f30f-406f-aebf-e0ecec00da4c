import ApiError from '@/utils/ApiError'
import logger from '@/config/logger'
import { Request, Response, NextFunction } from 'express'
import { IS_PRODUCTION } from '@/config/config'
import { BaseResponse } from '@/types/responses/base'
import { validationResult } from 'express-validator'

export const errorConverter = (err: Error, req: Request, res: Response, next: NextFunction) => {
  let error = err
  if (!(error instanceof ApiError)) {
    const statusCode = 500
    const message = error.message || 'Unknown server error.'
    error = new ApiError(statusCode, 'E500', message as string, err.stack);    
  }

  res.status(200).json({status: 200, "error_code": 1, code: 'E500', "message": error.message})
  //next(error)
}

// 【NOTE】nextパラメーターがない場合、errorHandlerが呼び出されていないので、Disable ESLintを付けました
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const errorHandler = (err: ApiError, _req: Request, res: Response, next: NextFunction) => {
  const { httpStatus, code, message } = err

  const response: BaseResponse = {
    code
  }

  if (httpStatus === 500) {
    logger.error(err)
  }
  if (!IS_PRODUCTION && httpStatus >= 400 && httpStatus < 500) {
    logger.debug(err)
  }

  if (httpStatus === 400 && message) {
    const errors = validationResult(_req)
    let errResponse
    if (!errors.isEmpty()) {
      errResponse = {
        status: 200,
        code,
        message,
        data: {
          errors: errors.array()
        }
      }
    } else {
      errResponse = {
        status: 200,
        code,
        message
      }
    }
    res.status(httpStatus).json(errResponse)
  } else if (message) {
    const errResponse = {
      status: 200,
      code,
      message
    }
    res.status(httpStatus).json(errResponse)
  } else {
    res.status(httpStatus).json(response)
  }
}
