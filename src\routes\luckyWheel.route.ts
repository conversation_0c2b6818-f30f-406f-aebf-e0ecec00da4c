import express from 'express'
import { luckyWheelAPI } from '@/api'
import { authenticate } from '@/middlewares/auth'

const luckyWheelRouter = express.Router()

luckyWheelRouter.post('/spin', authenticate, luckyWheelAPI.spinLuckyWheelHandler)
luckyWheelRouter.get('/prizes', luckyWheelAPI.getActivePrizesHandler)
luckyWheelRouter.post('/add-spin', authenticate, luckyWheelAPI.addSpinCountHandler)
luckyWheelRouter.get('/history', authenticate, luckyWheelAPI.getSpinHistoryHandler)
luckyWheelRouter.get('/stats/daily', luckyWheelAPI.getDailyStatsController)
luckyWheelRouter.post('/customer-info', authenticate, luckyWheelAPI.getInfoCustomerHandler)


const router = express.Router()
router.use('/lucky-wheel', luckyWheelRouter)

export default router
