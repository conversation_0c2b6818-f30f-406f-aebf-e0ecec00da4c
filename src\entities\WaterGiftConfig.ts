import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'

@Entity('water_gift_config')
export class WaterGiftConfig extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id' })
  id: number

  @Column('int', { name: 'game_id' })
  gameId: number

  @Column('int', { name: 'day' })
  day: number

  @Column('text', { name: 'config_fields' })
  configFields: string
} 