import express from 'express'
import { gameG1Service } from '@/services'
import { AuthorizedUserRequest } from '@/middlewares/auth'

/**
 * 1. GET /api/game/init
 * Initialize game, preload user data and game UI
 */
const initGameHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {
    const { slug } = req.params

    if (!slug) {
      return res.status(200).json({ 
        status: 'error', 
        message: 'Missing game slug' 
      })
    }

    const result = await gameG1Service.initGame(req, slug)
    return res.status(200).json(result)
  } catch (error) {
    return res.status(200).json({ 
      status: 'error', 
      message: error.message 
    })
  }
}

/**
 * 2. GET /api/game/play
 * Start the game and get vouchers that will fall
 */
const playGameHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {
    const { slug } = req.params

    if (!slug) {
      return res.status(200).json({ 
        status: 'error', 
        message: 'Missing game slug' 
      })
    }

    const result = await gameG1Service.playGame(req, slug)
    return res.status(200).json(result)
  } catch (error) {
    return res.status(200).json({ 
      status: 'error', 
      message: error.message 
    })
  }
}

/**
 * 3. POST /api/game/claim
 * Claim a voucher by tapping it
 */
const claimVoucherHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {
    const { slug } = req.params

    if (!slug) {
      return res.status(200).json({ 
        status: 'error', 
        message: 'Missing game slug' 
      })
    }

    const result = await gameG1Service.claimVoucher(
      req, 
      slug
    )
    return res.status(200).json(result)
  } catch (error) {
    return res.status(200).json({ 
      status: 'error', 
      message: error.message 
    })
  }
}

/**
 * 4. POST /api/game/share
 * Share to receive 1 extra play turn (1 time per day)
 */
const shareForExtraTurnHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {
    const { slug } = req.params
    const { platform } = req.body

    if (!slug) {
      return res.status(200).json({ 
        status: 'error', 
        message: 'Missing game slug' 
      })
    }

    if (!platform) {
      return res.status(200).json({ 
        status: 'error', 
        message: 'Missing platform' 
      })
    }

    const result = await gameG1Service.shareForExtraTurn(
      req, 
      slug, 
      platform
    )
    return res.status(200).json(result)
  } catch (error) {
    return res.status(200).json({ 
      status: 'error', 
      message: error.message 
    })
  }
}

export { 
  initGameHandler, 
  playGameHandler, 
  claimVoucherHandler, 
  shareForExtraTurnHandler 
} 