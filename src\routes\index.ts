import express from 'express'
import { apiRouter } from './api.route'

const router = express.Router()

// Disable Cache
router.use((_, res, next) => {
  res.setHeader('Cache-Control', 'no-store')
  next()
})

// Root router
router.get('/', (_, res) => {
  res.status(200).send('events')
})

router.get('/healthcheck', (_, res) => {
  res.status(200).send('success')
})

// API Router
router.use('/api-v1', apiRouter)

export default router
