import dotenv from 'dotenv'
import { DataSource } from 'typeorm'
import * as Entities from '@/entities'
import mysql from 'mysql2'

dotenv.config()
// App
export const ENVIRONMENT = process.env.APP_ENV || 'dev'
export const RUNS_IN = process.env.RUNS_IN || 'local'
export const IS_PRODUCTION = ENVIRONMENT === 'production'
export const IS_TEST = ENVIRONMENT === 'test'
export const APP_PORT = Number(process.env.APP_PORT) || 3001
export const MIN_CAPITAL = Number(process.env.MIN_CAPITAL) || 10
export const ENABLED_SOCKET = process.env.ENABLED_SOCKET === 'true' || false
// JWT
export const JWT = {
  //SECRET: process.env.JWT_SECRET || 'boVOsqGGZIJbkAJ8gDklnAN3cjePSrx5',
  SECRET: 'boVOsqGGZIJbkAJ8gDklnAN3cjePSrx5',
  ACCESS_TOKEN_EXPIRE: Number(process.env.JWT_ACCESS_TOKEN_EXPIRE) || 60 * 60 * 24 * 7,
  REFRESH_TOKEN_EXPIRE: Number(process.env.JWT_REFRESH_TOKEN_EXPIRE) || 60 * 60 * 24 * 30,
  RESOURCE_TOKEN_EXPIRE: Number(process.env.JWT_RESOURCE_TOKEN_EXPIRE) || 60 * 60 * 24 * 7
}
// DB
export const DB = {
  USER: process.env.DB_USER || 'root',
  PASSWORD: process.env.DB_PASSWORD || '',
  HOST: process.env.DB_HOST || '127.0.0.1',
  HOST_READ: process.env.DB_HOST_READ || '127.0.0.1',
  NAME: process.env.DB_NAME || 'copy-trade',
  PORT: Number(process.env.DB_PORT) || 3306
}

// export const DB = {
//   USER: "thanhnv",
//   PASSWORD: "uAqidLwlmoFWsjThIHaS",
//   HOST: "*********",
//   HOST_READ: "*********",
//   NAME: "kam_gift_admicro",
//   PORT: Number(process.env.DB_PORT) || 3306
// }

export const AppDataSource = new DataSource({
  type: 'mysql',
  charset: 'utf8mb4',
  replication: {
    master: {
      host: DB.HOST,
      port: DB.PORT,
      username: DB.USER,
      password: DB.PASSWORD,
      database: DB.NAME
    },
    slaves: [
      {
        host: DB.HOST_READ,
        port: DB.PORT,
        username: DB.USER,
        password: DB.PASSWORD,
        database: DB.NAME
      }
    ]
  },
  entities: Object.values(Entities),
  migrationsRun: false,
  synchronize: false,
  timezone: 'Z',
  logging: true,
  extra: {
    decimalNumbers: true
  }
})

// Redis
export const REDIS = {
  URI: process.env.REDIS_URI || 'redis://127.0.0.1',
  HOST: () => {
    const url = new URL(REDIS.URI)
    return url.host || '127.0.0.1'
  },
  PORT: () => {
    const url = new URL(REDIS.URI)
    return Number(url.port || 6379)
  },
  CLUSTER_MODE: process.env.REDIS_CLUSTER_MODE === 'true' || false,
  CLUSTER_HOST: process.env.REDIS_CLUSTER_HOST || '127.0.0.1',
  CLUSTER_PORT: Number(process.env.REDIS_CLUSTER_PORT) || 6379
}

const mySqlCnnString = {
	host     : process.env.DB_HOST,
  port     : Number(process.env.DB_PORT) || 3306,
	user     : process.env.DB_USER || 'root',
	password : process.env.DB_PASSWORD || '',
	database : process.env.DB_NAME || 'copy-trade',
	connectionLimit : 10,
  timezone: 'Asia/Ho_Chi_Minh'
};

export const MySqlCnn = () => {
  //Establish Connection to the DB
  const connection = mysql.createConnection(mySqlCnnString);

  //Instantiate the connection
  connection.connect(function (err) {
      if (err) {
          //console.log(`connectionRequest Failed ${err.stack}`)
      } else {
          //console.log(`DB connectionRequest Successful ${connection.threadId}`)
      }
  });

  return connection
}
