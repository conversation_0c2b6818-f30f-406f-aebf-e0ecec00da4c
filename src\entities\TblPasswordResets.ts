import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'

@Entity('password_resets', { schema: 'events' })
export class TblPasswordResets extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('varchar', { name: 'email', length: 255 })
  email: string

  @Column('varchar', { name: 'token', length: 255 })
  token: string
}
