import express from 'express'
import { user<PERSON><PERSON> } from '@/api'
import { noAuthenticate, clientAuthenticate, authenticate, authenticateUpdate } from '@/middlewares/auth'

// user management router
const userRouter = express.Router();
//userRouter.use(authenticate)

userRouter.get('/get-info', authenticate, userAPI.getInfo);
userRouter.post('/app-event/update', clientAuthenticate, userAPI.updateInfo);

userRouter.post('/reg-by-email', noAuthenticate, userAPI.regUserByEmailHandler);
//userRouter.get('/verify-otp-via-email', noAuthenticate, userAPI.verifyOtpViaEmailHandler);
userRouter.post('/verify-otp-via-email', noAuthenticate, userAPI.verifyOtpViaEmailPostHandler);
userRouter.post('/recover-password-by-email', noAuthenticate, userAPI.recoverPasswordByEmailHandler);
userRouter.post('/reset-password-by-email', noAuthenticate, userAPI.resetPasswordByEmailHandler);
userRouter.post('/change-password-by-email', noAuthenticate, userAPI.changePasswordByEmailHandler);

userRouter.post('/valid-otp', noAuthenticate, userAPI.validOtpHandler);

userRouter.post('/reg-by-phone', noAuthenticate, userAPI.regUserByPhoneHandler);
userRouter.post('/verify-otp-via-phone', noAuthenticate, userAPI.verifyOtpViaPhoneHandler);
userRouter.post('/recover-password-by-phone', noAuthenticate, userAPI.recoverPasswordByPhoneHandler);
userRouter.post('/reset-password-by-phone', noAuthenticate, userAPI.resetPasswordByPhoneHandler);

userRouter.post('/reg-by-google', noAuthenticate, userAPI.regUserByGoogleHandler);
userRouter.post('/reg-by-facebook', noAuthenticate, userAPI.regUserByFacebookHandler);

userRouter.put('/:id', authenticateUpdate, userAPI.updateUser);

userRouter.post('/update-info', authenticate, userAPI.updateUserInfo);

//phone contact
userRouter.post('/req-verify-phone-contact', authenticate, userAPI.reqVerifyPhoneContactHandler);
userRouter.post('/verify-otp-via-phone-contact', authenticate, userAPI.verifyOtpViaPhoneContactHandler);
userRouter.post('/reload-zalo-authen', authenticate, userAPI.reloadZaloAuthenHandler);

userRouter.post('/test-sends-sms-by-zalo', authenticate, userAPI.testSendSmsByZalo);


// Export router
const router = express.Router();
router.use('/user', userRouter);

export default router
