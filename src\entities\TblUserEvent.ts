import { Column, <PERSON>ti<PERSON>, PrimaryGeneratedColumn, ManyToOne, JoinColumn } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'
import { TblUsers } from './TblUsers'
import { TblGames } from './TblGames'

@Entity('user_event', { schema: 'events' })
export class TblUserEvent extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('int', { name: 'user_id' })
  userId: number

  @Column('int', { name: 'event_id' })
  eventId: number

  @Column('int', { name: 'spins' })
  spins: number

  @Column('varchar', { name: 'code' })
  code: string

  @ManyToOne(() => TblUsers, { eager: false })
  @JoinColumn({ name: 'user_id' })
  user: TblUsers

  @ManyToOne(() => TblGames, { eager: false })
  @JoinColumn({ name: 'event_id' })
  event: TblGames
}
