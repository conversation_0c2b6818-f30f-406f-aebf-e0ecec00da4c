//export const NOTIFICATION_URL = 'https://dev.notify-service.admicro.vn/v3/eventticket'
export const NOTIFICATION_URL = 'https://notify-service.admicro.vn/v3/eventticket/'

export const VOUCHER_PROJECT_TOKEN = 'ea89b9ac-6b71-4575-afc2-1b0a883ae392'
export const VOUCHER_CLIENT_ID = '23947'
export const VOUCHER_ADD_ENDPOINT = 'https://coupon.bizfly.vn/api/sdk/storage/get-coupon'
export const VOUCHER_USED_ENDPOINT = 'https://coupon.bizfly.vn/api/sdk/storage/tick-coupon-by-code'
export const VOUCHER_HEADER_TOKEN = '85e7c0f4022300a68699ddfa796ecff8ad1e16439bad0927a2f08bdf8c4584e2'
export const VOUCHER_LANDING_DETAIL = 'https://hybrid-event.admicro.vn/voucher/:id_voucher/:user_id/:token'

export const API_UPLOAD_AVATAR = 'https://cms-event.admicro.vn/api/v1/upload'

export const MAP_TOKEN = '10|NUXs1R56xavld7RlM19Q431Ow6YcvdrGE7Jb1atD7fa95dd7'
export const MAP_LISTPOINT_ENDPOINT = 'https://ticket.bctoyz.com/api/project/[PROJECT_ID]/events'
export const MAP_CONFIG_ENDPOINT = 'https://ticket.bctoyz.com/api/project/[PROJECT_ID]/map-config'
export const MAP_LAYER_ENDPOINT = 'https://ticket.bctoyz.com/api/project/[PROJECT_ID]/layers'

export const OTP_TIME_EXPIRE = 5 * 60 //otp time will be expired in 5 mins

export const API_BASE_URL_COUPON = 'https://coupon.bizfly.vn/api/sdk'
export const API_ENDPOINTS = {
    getCoupon: `${API_BASE_URL_COUPON}/storage/get-coupon`,
    updateTableBase: `https://crm.bizfly.vn/_api/base-table/update`,
    getTableBase: `https://api.bizfly.vn/crm/_api/base-table/find`,
}
//mail
/*
    Server: smtp.bizflycloud.vn
    Port: 587
    Phương thức mã hóa: STARTTLS

    user: <EMAIL>
    pass: u3nJd5#0B~L>
*/

// export const MAIL_TEMP = "@temp.com";
// export const MAIL_HOST = '**********'
// export const MAIL_POST = 2525
// export const MAIL_SECURE = false
// export const MAIL_USER = '<EMAIL>'
// export const MAIL_PASS = '43dRLbQY243eczpKsY6M'
export const MAIL_TEMP = "@temp.com";
export const MAIL_HOST = 'smtp.bizflycloud.vn'
export const MAIL_POST = 587
export const MAIL_SECURE = false
export const MAIL_USER = '<EMAIL>'
export const MAIL_PASS = 'u3nJd5#0B~L>'
export const MAIL_FIX_LIST = ['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>'];
export const USER_NO_VERIFY = 2;
export const USER_VERIFIED = 1;


//export const ACTIVATE_EMAIL_LINK = `https://api-kam-gift.admicro.vn/api/v1/auth/verify-otp-via-email/?e=[EMAIL]&o=[OTP]&k=[KEY]`;
export const ACTIVATE_EMAIL_LINK = (process.env.APP_ENV == 'dev') ? 'https://dev.api-kam-gift.admicro.vn/api/v1/auth/verify-otp-via-email/?e=[EMAIL]&o=[OTP]&k=[KEY]': 'https://tangqua.afamily.vn/api/v1/auth/verify-otp-via-email/?e=[EMAIL]&o=[OTP]&k=[KEY]';
//export const ACTIVATE_EMAIL_LINK = 'https://tangqua.afamily.vn/api/v1/auth/verify-otp-via-email/?e=[EMAIL]&o=[OTP]&k=[KEY]';

//export const EMAIL_OTP_VERIFY_LINK = `https://dev.kamgift.admicro.vn/emailVerify?email=[EMAIL]&token=[KEY]`;
export const EMAIL_OTP_VERIFY_LINK = (process.env.APP_ENV == 'dev') ? 'https://dev.tangqua.kam-gift.admicro.vn/emailVerify?email=[EMAIL]&token=[KEY]': 'https://tangqua.afamily.vn/emailVerify?email=[EMAIL]&token=[KEY]';
//export const EMAIL_OTP_VERIFY_LINK = 'https://tangqua.afamily.vn/emailVerify?email=[EMAIL]&token=[KEY]';

export const REG_MAIL_CONTENT = `<p>Xin chào!
        
<p>Vui lòng nhấn vào nút bên dưới để xác thực địa chỉ email của bạn.
<p><a href = "[LINK_VERIFY_EMAIL]">Xác thực Email </a>
<p>Nếu bạn không tạo tài khoản này, vui lòng bỏ qua email này.

<p>Xin cám ơn.</p>`;


export const EMAIL_OTP_FORGOTPASS_LINK = (process.env.APP_ENV == 'dev') ? `https://dev.tangqua.kam-gift.admicro.vn/recoveryPassword?email=[EMAIL]&otp=[KEY]` : `https://tangqua.afamily.vn/recoveryPassword?email=[EMAIL]&otp=[KEY]`;
//export const EMAIL_OTP_FORGOTPASS_LINK = `https://tangqua.afamily.vn/recoveryPassword?email=[EMAIL]&otp=[KEY]`;
export const FORGOTPASS_MAIL_CONTENT = `<p>Xin chào!
        
<p>Vui lòng nhấn vào nút bên dưới để xác thực địa chỉ email của bạn.
<p><a href = "[LINK_VERIFY_EMAIL]">Xác thực Email </a>
<p>Nếu bạn lấy lại mật khẩu tài khoản này, vui lòng bỏ qua email này.

<p>Xin cám ơn.</p>`;

//sms
export const SMS_HOST = process.env.SMS_HOST || 'http://api.bipbip.vn/api/send'
export const SMS_USER = process.env.SMS_USER || 'kamgift'
export const SMS_PASS = process.env.SMS_PASS || 'M0kjn2H9f2kmg'
export const SMS_BRAND_NAME = process.env.SMS_BRAND_NAME || 'VCCorp'
export const SMS_BRAND_CONTENT = process.env.SMS_BRAND_CONTENT || '[OTP] la ma OTP cua ban. Dung chia se ma nay voi bat ky ai'

export const GUEST_ACCESS_TOKEN = '00eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9212';
export const GUEST_ROLE_ID = 13;
export const USER_ONLINE_ROLE_ID = 12;

//google
// export const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID ||'534851238305-08g0o2t751mi0f5s06996c0nmnk3c0fs.apps.googleusercontent.com'
// export const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET || '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'
// export const GOOGLE_CALLBACK = process.env.GOOGLE_CALLBACK || 'https://api-kam-gift.admicro.vn/api/v1/auth/google/callback'
export const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID ||'25334266284-e3l9qufia33n8vv95ufumg34mb99d1ko.apps.googleusercontent.com'
export const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET || 'GOOGLE_CLIENT_SECRET=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'
export const GOOGLE_CALLBACK = process.env.GOOGLE_CALLBACK || 'https://tangqua.afamily.vn/api/auth/callback/google'

//facebook
export const FACEBOOK_CLIENT_ID = process.env.FACEBOOK_CLIENT_ID || '1972932676174616'
export const FACEBOOK_CLIENT_SECRET= process.env.FACEBOOK_CLIENT_SECRET || '********************************'
export const FACEBOOK_CALLBACK = process.env.FACEBOOK_CALLBACK || '/auth/facebook/callback'

export const SESSION_SECRET='S1!s8&dKj23@aP#MlN40$WqR'