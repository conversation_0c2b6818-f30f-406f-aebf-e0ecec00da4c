import { Column, Entity, PrimaryGeneratedColumn, ManyToOne, JoinColumn } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'
import { WaterGiftConfig } from './WaterGiftConfig'


@Entity('warter_user_gift')
export class WaterUserGift extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id' })
  id: number

  @Column('int', { name: 'game_id' })
  gameId: number

  @Column('int', { name: 'campaign_id' })
  campaignId: number

  @Column('int', { name: 'user_id' })
  userId: number

  @Column('int', { name: 'times' })
  times: number

  @Column('int', { name: 'curr_water_gift_config_id' })
  currWaterGiftConfigId: number

  @Column('varchar', { name: 'reward_code', length: 765 })
  rewardCode: string

  @Column('tinyint', { name: 'active' })
  active: number

    // Relationship với WaterGiftConfig
  @ManyToOne(() => WaterGiftConfig)
  @JoinColumn({ name: 'curr_water_gift_config_id' })
  waterGiftConfig: WaterGiftConfig
} 