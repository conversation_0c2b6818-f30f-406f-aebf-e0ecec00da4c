import { AppDataSource } from '@/config/config'
import { UserSpin, LuckyPrize, SpinHistory, TblUsers, TblGames } from '@/entities'
import { AuthorizedUserRequest } from '@/middlewares/auth'
import { luckyWheelService } from '@/services'
import { NotFoundError } from '@/utils/ApiError'
import moment from 'moment'

// Helper functions
const userSpinRepo = () => AppDataSource.getRepository(UserSpin)
const prizeRepo = () => AppDataSource.getRepository(LuckyPrize)
const spinHistoryRepo = () => AppDataSource.getRepository(SpinHistory)
const gameRepo = () => AppDataSource.getRepository(TblGames)

// Helper function to get current time in GMT+7
const getNowGMT7 = () => {
  return moment.utc().add(7, 'hours').toDate();
}

// Helper function to convert gameId slug to number
const convertGameIdSlugToNumber = (gameIdSlug: string): number => {
  switch (gameIdSlug) {
    case 'g1':
      return 27;
    case 'g2':
      return 28;
    // Thêm các case khác nếu cần
    default:
      return parseInt(gameIdSlug) || 0
  }
}

// Helper function to check if it's same day (Vietnam timezone)
const isSameDay = (date1: Date, date2: Date): boolean => {
  const d1 = new Date(date1.getTime() - (7 * 3600000)) // Convert to GMT+7
  const d2 = new Date(date2.getTime() - (7 * 3600000)) // Convert to GMT+7
  return d1.toDateString() === d2.toDateString()
}

/**
 * 1. GET /api/game/init
 * Initialize game, preload user data and game UI
 */
export const initGame = async (req: AuthorizedUserRequest, gameIdSlug: string) => {
  const { user } = req.authorizedUser
  const gameId = convertGameIdSlugToNumber(gameIdSlug)
  
  if (!gameId) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  // Lấy thông tin game để có campaignId
  const game = await gameRepo().findOne({
    where: { id: gameId }
  })
  
  if (!game) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  const campaignId = game.campaignId

  // Lấy thông tin UserSpin
  let userSpin = await userSpinRepo().findOne({
    where: { userId: user.id, campaignId, gameId }
  })

  // Nếu chưa có UserSpin, tạo mới với số lượt mặc định
  if (!userSpin) {
    const now = getNowGMT7()
    userSpin = new UserSpin()
    userSpin.userId = user.id
    userSpin.campaignId = campaignId
    userSpin.gameId = gameId
    userSpin.spinCounts = 1 // Số lượt chơi mặc định mỗi ngày
    userSpin.points = 0
    userSpin.createdAt = now
    userSpin.updatedAt = now
    userSpin.lastRequest = now
    await userSpinRepo().save(userSpin)
  } else {
    if(userSpin.spinCounts == 0){
        const now = getNowGMT7()
    
        // Kiểm tra nếu lastRequest là ngày hôm trước thì cộng thêm 1 lượt cho ngày mới
        if (!userSpin.lastRequest || !isSameDay(userSpin.lastRequest, now)) {
            userSpin.spinCounts += 1
            userSpin.lastRequest = now
        }
        
        userSpin.updatedAt = now
        await userSpinRepo().save(userSpin)
    }
  }

  return {
    status: 'success',
    user: {
      id: user.id,
      name: user.name
    },
    play_turns: userSpin.spinCounts
  }
}

/**
 * 2. GET /api/game/play
 * Start the game and get vouchers that will fall
 */
export const playGame = async (req: AuthorizedUserRequest, gameIdSlug: string) => {
  const { user } = req.authorizedUser
  const gameId = convertGameIdSlugToNumber(gameIdSlug)
  
  if (!gameId) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  // Lấy thông tin game để có campaignId
  const game = await gameRepo().findOne({
    where: { id: gameId }
  })
  
  if (!game) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  const campaignId = game.campaignId

  // Lấy thông tin UserSpin
  const userSpin = await userSpinRepo().findOne({
    where: { userId: user.id, campaignId, gameId }
  })

  if (!userSpin) {
    throw new NotFoundError('Vui lòng khởi tạo game trước', '', 1)
  }

  // Lấy danh sách phần quà (vouchers)
  const prizes = await prizeRepo().find({
    where: { campaignId, gameId, active: 1 },
    order: { displayOrder: 'ASC' }
  })

  const vouchers = prizes.map(prize => ({
    id: prize.bizStorageId || prize.id,
    value: prize.name,
    image: prize.image
  }))

  return {
    status: 'success',
    play_turns: userSpin.spinCounts,
    vouchers
  }
}

/**
 * 3. POST /api/game/claim
 * Claim a voucher by tapping it
 */
export const claimVoucher = async (req: AuthorizedUserRequest, gameIdSlug: string, listBoxId: Array<number> = []) => {
  const { user } = req.authorizedUser
  const gameId = convertGameIdSlugToNumber(gameIdSlug)
  
  if (!gameId) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }
  
  const game = await gameRepo().findOne({
    where: { id: gameId }
  })
  
  if (!game) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  const campaignId = game.campaignId
  
  // Lấy thông tin UserSpin
  const userSpin = await userSpinRepo().findOne({
    where: { userId: user.id, campaignId, gameId }
  })

  if (!userSpin) {
    throw new NotFoundError('Vui lòng khởi tạo game trước', '', 1)
  }

  // Nếu còn lượt chơi
  if (userSpin.spinCounts > 0) {
    const prizes = await luckyWheelService.getActivePrizes(campaignId, game.id);
    
    if (gameIdSlug === 'g1') {
      // Logic cho game G1 (giữ nguyên)
      let couponData = null
      let message = `Đã hết voucher này`;
      let status = 'false';
      
      const prize = luckyWheelService.randomPrize(prizes)

      if (!prize) {
        throw new NotFoundError('Không có phần thưởng nào', '', 1)
      }

      // Nếu là voucher và có bizStorageId, simulated API call (vì getCouponFromBizfly không export)
      if (prize.type === 'voucher' && prize.bizStorageId) {
        try {
          // Đếm số lần prize này đã được trúng
          const wonCount = await spinHistoryRepo()
          .createQueryBuilder('spin_history')
          .where('spin_history.prizeId = :prizeId', { prizeId: prize.id })
          .getCount()

          const remainingQuantity = prize.quantity - wonCount;

          if (remainingQuantity > 0) {
              const couponResponse = await luckyWheelService.getCouponFromBizfly(user.id, prize.bizStorageId, user.tokyoId, user.bizId, game.name, campaignId, gameId, 4)
              if (couponResponse && couponResponse.data) {
                  couponData = {
                      couponId: couponResponse.data.coupon_id,
                      couponCode: couponResponse.data.code,
                      couponName: couponResponse.data.name,
                      qrCodeLink: couponResponse.data.link_scan_qr_code
                  }
                  message = `Bạn nhận được ${prize.name}`
                  status = 'success';
              }
          }
        } catch (error) {
          console.error('Error getting coupon:', error)
        }
      }
      
      // Lưu vào SpinHistory
      await luckyWheelService.saveSpinHistory(user.id, prize.id || 0, campaignId, gameId, couponData)

      // Trừ lượt chơi
      userSpin.spinCounts -= 1
      userSpin.updatedAt = getNowGMT7()
      await userSpinRepo().save(userSpin)

      return {
        status,
        message,
        play_turns: userSpin.spinCounts
      }
    } 
    else if (gameIdSlug === 'g2') {
      // Logic cho game G2 - Box opening game
      const MAX_WINNING_BOXES = 1; // Số box có thể trúng cùng lúc (có thể config)
      
      // Lọc prizes theo type
      const winPrizes = prizes.filter(p => p.type !== 'lose');
      const losePrizes = prizes.filter(p => p.type === 'lose');
      
      if (winPrizes.length === 0) {
        throw new NotFoundError('Không có phần thưởng win nào', '', 1)
      }

      // Tạo danh sách 9 boxes với reward
      const boxes = Array(9).fill(null);
      const sentBoxIds = listBoxId.length > 0 ? listBoxId : [];
      
      // Random các winning boxes từ box_ids được gửi lên
      const winningBoxCount = Math.min(MAX_WINNING_BOXES, sentBoxIds.length);
      const winningBoxes = [];
      const remainingSentBoxes = [...sentBoxIds];
      
      // Chọn random winning boxes từ sentBoxIds
      for (let i = 0; i < winningBoxCount; i++) {
        const randomIndex = Math.floor(Math.random() * remainingSentBoxes.length);
        winningBoxes.push(remainingSentBoxes.splice(randomIndex, 1)[0]);
      }

      // Lưu trữ các prize đã sử dụng trong sentBoxIds để tránh lặp lại
      const usedPrizeNames = new Set();
      
      // Fill các box được gửi lên
      for (const boxId of sentBoxIds) {
        const boxIndex = boxId - 1;
        
        if (winningBoxes.includes(boxId)) {
          // Box trúng - lấy prize từ winPrizes
          const availableWinPrizes = winPrizes.filter(p => !usedPrizeNames.has(p.name));
          const prizeToUse = availableWinPrizes.length > 0 ? availableWinPrizes : winPrizes;
          const prizeIndex = Math.floor(Math.random() * prizeToUse.length);
          const prize = prizeToUse[prizeIndex];
          
          boxes[boxIndex] = {
            id: boxId,
            reward: {
              name: prize.name,
              image: prize.image
            },
            prizeData: prize // Lưu thông tin prize để xử lý coupon
          };
          usedPrizeNames.add(prize.name);
        } else {
          // Box không trúng trong danh sách gửi lên - dùng lose prizes
          if (losePrizes.length > 0) {
            const prizeIndex = Math.floor(Math.random() * losePrizes.length);
            const prize = losePrizes[prizeIndex];
            boxes[boxIndex] = {
              id: boxId,
              reward: {
                name: prize.name,
                image: prize.image
              }
            };
          } else {
            // Nếu không có lose prizes, dùng win prizes
            const availableWinPrizes = winPrizes.filter(p => !usedPrizeNames.has(p.name));
            const prizeToUse = availableWinPrizes.length > 0 ? availableWinPrizes : winPrizes;
            const prizeIndex = Math.floor(Math.random() * prizeToUse.length);
            const prize = prizeToUse[prizeIndex];
            boxes[boxIndex] = {
              id: boxId,
              reward: {
                name: prize.name,
                image: prize.image
              }
            };
            usedPrizeNames.add(prize.name);
          }
        }
      }

      // Fill các box còn lại (không được gửi lên) bằng tất cả prizes (win + lose) trừ đã dùng
      const allPrizes = [...winPrizes, ...losePrizes];
      const availablePrizesForRemaining = allPrizes.filter(p => !usedPrizeNames.has(p.name));
      const prizesToUseForRemaining = availablePrizesForRemaining.length > 0 ? availablePrizesForRemaining : allPrizes;
      
      // Shuffle prizes để random thứ tự
      const shufflePrizes = (array: any[]) => {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
          const j = Math.floor(Math.random() * (i + 1));
          [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
      };
      
      const shuffledPrizes = shufflePrizes(prizesToUseForRemaining);
      let remainingPrizeIndex = 0;
      
      for (let i = 1; i <= 9; i++) {
        if (boxes[i - 1] === null) {
          // Box chưa được fill - lấy prize với thứ tự đã shuffle
          const prize = shuffledPrizes[remainingPrizeIndex % shuffledPrizes.length];
          
          boxes[i - 1] = {
            id: i,
            reward: {
              name: prize.name,
              image: prize.image
            }
          };
          remainingPrizeIndex++;
        }
      }

      // Xử lý coupon cho từng winning box
      const couponDataList = [];
      let message = 'Chúc bạn may mắn lần sau!';
      const status = 'success';

      if (winningBoxes.length > 0) {
        for (const winningBoxId of winningBoxes) {
          const winningBox = boxes.find(box => box.id === winningBoxId && box.prizeData);
          if (winningBox && winningBox.prizeData) {
            const winningPrize = winningBox.prizeData;
            
            if (winningPrize.type === 'voucher' && winningPrize.bizStorageId) {
              try {
                const wonCount = await spinHistoryRepo()
                  .createQueryBuilder('spin_history')
                  .where('spin_history.prizeId = :prizeId', { prizeId: winningPrize.id })
                  .getCount()

                const remainingQuantity = winningPrize.quantity - wonCount;

                if (remainingQuantity > 0) {
                  const couponResponse = await luckyWheelService.getCouponFromBizfly(user.id, winningPrize.bizStorageId, user.tokyoId, user.bizId, game.name, campaignId, gameId, 4)
                  if (couponResponse && couponResponse.data) {
                    const couponData = {
                      couponId: couponResponse.data.coupon_id,
                      couponCode: couponResponse.data.code,
                      couponName: couponResponse.data.name,
                      qrCodeLink: couponResponse.data.link_scan_qr_code
                    }
                    couponDataList.push(couponData);
                  }
                }
              } catch (error) {
                console.error('Error getting coupon:', error)
              }
            }

            // Lưu vào SpinHistory cho mỗi box trúng
            await luckyWheelService.saveSpinHistory(user.id, winningPrize.id || 0, campaignId, gameId, couponDataList[couponDataList.length - 1] || null)
          }
        }
        
        const boxNumbers = winningBoxes.join(', ');
        message = `Bạn đã mở hộp quà số ${boxNumbers} và nhận được voucher!`;
      }

      // Trừ lượt chơi
      userSpin.spinCounts -= 1
      userSpin.updatedAt = getNowGMT7()
      await userSpinRepo().save(userSpin)

      // Loại bỏ prizeData khỏi response
      const responseBoxes = boxes.map(box => ({
        id: box.id,
        reward: box.reward
      }));

      return {
        status,
        message,
        boxes: responseBoxes,
        play_turns: userSpin.spinCounts
      }
    }
  } else {
    // Hết lượt chơi - lấy danh sách phần thưởng đã nhận
    const receivedRewards = await spinHistoryRepo()
      .createQueryBuilder('spin_histories')
      .leftJoinAndSelect('spin_histories.prize', 'prize')
      .where('spin_histories.userId = :userId', { userId: user.id })
      .andWhere('spin_histories.campaignId = :campaignId', { campaignId })
      .andWhere('spin_histories.gameId = :gameId', { gameId })
      .getMany()

    const rewards = receivedRewards.map(history => ({
      id: history.prize.id,
      name: history.voucherName || history.prize.name
    }))

    // Kiểm tra share_remaining
    const now = getNowGMT7()
    const shareRemaining = userSpin.lastRequestFacebook && isSameDay(userSpin.lastRequestFacebook, now) ? 0 : 1

    if (gameIdSlug === 'g2') {
      // Cho game G2, trả về tất cả boxes khi hết lượt
      const prizes = await luckyWheelService.getActivePrizes(campaignId, game.id);
      const winPrizes = prizes.filter(p => p.type !== 'lose');
      const losePrizes = prizes.filter(p => p.type === 'lose');
      const allPrizes = [...winPrizes, ...losePrizes];
      
      // Shuffle prizes để random thứ tự
      const shufflePrizes = (array: any[]) => {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
          const j = Math.floor(Math.random() * (i + 1));
          [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
      };
      
      const shuffledAllPrizes = shufflePrizes(allPrizes);
      
      const boxes = [];
      for (let i = 1; i <= 9; i++) {
        const prizeIndex = (i - 1) % shuffledAllPrizes.length;
        const prize = shuffledAllPrizes[prizeIndex];
        boxes.push({
          id: i,
          reward: {
            name: prize.name,
            image: prize.image
          }
        });
      }

      return {
        status: 'no_turns',
        message: 'Bạn đã hết lượt chơi hôm nay',
        boxes,
        share_remaining: shareRemaining
      }
    }

    return {
      status: 'no_turns',
      message: 'Bạn đã hết lượt chơi hôm nay',
      received_rewards: rewards,
      share_remaining: shareRemaining
    }
  }
}

/**
 * 4. POST /api/game/share
 * Share to receive 1 extra play turn (1 time per day)
 */
export const shareForExtraTurn = async (req: AuthorizedUserRequest, gameIdSlug: string, platform: string) => {
  const { user } = req.authorizedUser
  const gameId = convertGameIdSlugToNumber(gameIdSlug)
  
  if (!gameId) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  // Lấy thông tin game để có campaignId
  const game = await gameRepo().findOne({
    where: { id: gameId }
  })
  
  if (!game) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  const campaignId = game.campaignId

  // Lấy thông tin UserSpin
  const userSpin = await userSpinRepo().findOne({
    where: { userId: user.id, campaignId, gameId }
  })

  if (!userSpin) {
    throw new NotFoundError('Vui lòng khởi tạo game trước', '', 1)
  }

  const now = getNowGMT7()
   // Kiểm tra đã share facebook hôm nay chưa
  if(platform === 'facebook'){
    if (userSpin.lastRequestFacebook && isSameDay(userSpin.lastRequestFacebook, now)) {
        return {
            status: 'already_shared',
            message: 'Bạn đã chia sẻ và nhận lượt hôm nay rồi'
        }
    }
    // Cộng thêm 1 lượt chơi
    userSpin.spinCounts += 1
    userSpin.lastRequestFacebook = now
    userSpin.updatedAt = now
    await userSpinRepo().save(userSpin)

    return {
        status: 'success',
        message: 'Bạn đã nhận thêm 1 lượt chơi!',
        play_turns: userSpin.spinCounts
      }
  }else{
    return {
        status: 'error',
        message: 'Không hỗ trợ platform này'
    }
  }
} 