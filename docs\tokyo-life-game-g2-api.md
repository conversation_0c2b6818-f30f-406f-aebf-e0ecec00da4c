# Tokyo Life Game - API Documentation - G2

## Authentication
All requests require authentication via <PERSON><PERSON>.

**Header Example:**

```

Authorization: Bearer <token>

````

---

## 1. GET `/api/game/init`

**Description:** Initialize game, preload user data and game UI.

**Response:**
```json
{
  "status": "success",
  "user": {
    "id": 123,
    "name": "Nguyen Van A"
  },
  "play_turns": 3
}
````

---

## 2. POST `/api/game/claim`

**Description:** Claim a voucher by tapping it.

**Request:**

```json
{
  "box_ids": [1,6,9]
}
```

**Response (if turns remaining):**

```json
{
  "status": "success",
  "message": "Bạn đã mở hộp quà số 1, 6, 9 và nhận được voucher!",
  "boxes": [
    {"id": 1, "reward": {"name": "Voucher 20K", "image": "/assets/v20k.png"}},
    {"id": 2, "reward": {"name": "Voucher 50K", "image": "/assets/v50k.png"}},
    {"id": 3, "reward": {"name": "Voucher 100K", "image": "/assets/v100k.png"}},
    {"id": 4, "reward": {"name": "Voucher 150K", "image": "/assets/v150k.png"}},
    {"id": 5, "reward": {"name": "Voucher 200K", "image": "/assets/v200k.png"}},
    {"id": 6, "reward": {"name": "Voucher 250K", "image": "/assets/v250k.png"}},
    {"id": 7, "reward": {"name": "Voucher 300K", "image": "/assets/v300k.png"}},
    {"id": 8, "reward": {"name": "Voucher 350K", "image": "/assets/v350k.png"}},
    {"id": 9, "reward": {"name": "Voucher 400K", "image": "/assets/v400k.png"}}
  ],
  "play_turns": 2
}
```

**Response (if no turns left):**

```json
{
  "status": "no_turns",
  "message": "Bạn đã hết lượt chơi hôm nay",
  "boxes": [
    {"id": 1, "reward": {"name": "Voucher 20K", "image": "/assets/v20k.png"}},
    {"id": 2, "reward": {"name": "Voucher 50K", "image": "/assets/v50k.png"}},
    {"id": 3, "reward": {"name": "Voucher 100K", "image": "/assets/v100k.png"}},
    {"id": 4, "reward": {"name": "Voucher 150K", "image": "/assets/v150k.png"}},
    {"id": 5, "reward": {"name": "Voucher 200K", "image": "/assets/v200k.png"}},
    {"id": 6, "reward": {"name": "Voucher 250K", "image": "/assets/v250k.png"}},
    {"id": 7, "reward": {"name": "Voucher 300K", "image": "/assets/v300k.png"}},
    {"id": 8, "reward": {"name": "Voucher 350K", "image": "/assets/v350k.png"}},
    {"id": 9, "reward": {"name": "Voucher 400K", "image": "/assets/v400k.png"}}
  ],
  "share_remaining": 1
}
```

---

## 3. POST `/api/game/share`

**Description:** Share to receive 1 extra play turn (1 time per day).

**Request:**

```json
{
  "platform": "facebook"
}
```

**Response:**

```json
{
  "status": "success",
  "message": "Bạn đã nhận thêm 1 lượt chơi!",
  "play_turns": 1
}
```

**If already shared today:**

```json
{
  "status": "already_shared",
  "message": "Bạn đã chia sẻ và nhận lượt hôm nay rồi"
}
```

---

## Notes

* **Play turns reset daily** at 00:00.
* **Share for extra turn** is limited to once per day.
* **Voucher claim is only valid if `play_turns` > 0.**
