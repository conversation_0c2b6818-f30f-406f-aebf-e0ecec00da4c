import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'

@Entity('poll_score_levels')
export class TblPollScoreLevels extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id' })
  id: number

  @Column('int', { name: 'poll_id' })
  pollId: number

  @Column('int', { name: 'min_score' })
  minScore: number

  @Column('int', { name: 'max_score' })
  maxMcore: number

  @Column('varchar', { name: 'title', length: 255 })
  title: string

  @Column('varchar', { name: 'description'})
  description: string

  @Column('tinyint', { name: 'status' })
  status: number
}