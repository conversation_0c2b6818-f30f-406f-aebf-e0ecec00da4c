import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm'

@Entity('log_activities')
export class LogActivity {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: number

  @Column({ type: 'bigint', nullable: true })
  user_id: number

  @Column({ length: 255, nullable: true })
  name: string

  @Column({ type: 'text', nullable: true })
  message: string

  @Column({ type: 'text', nullable: true })
  full_message: string

  @Column({ length: 255, nullable: true })
  url: string

  @Column({ length: 255, nullable: true })
  method: string

  @Column({ length: 255, nullable: true })
  ip: string

  @Column({ length: 255, nullable: true })
  agent: string

  @Column({ type: 'text', nullable: true })
  form_data: string

  @CreateDateColumn()
  created_at: Date

  @UpdateDateColumn()
  updated_at: Date
}
