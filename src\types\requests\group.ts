import { AuthorizedUserRequest } from '@/middlewares/auth'
import { BaseSearchRequest } from './base'
import { GroupStatus } from '@/entities/TblGroups'
import { TblUsers } from '@/entities/TblUsers'

export interface CreateGroupRequest extends AuthorizedUserRequest {
  name: string
  description: string
  status: GroupStatus | null
}

export interface UpdateGroupRequest extends AuthorizedUserRequest {
  id: number
  name: string
  description: string
  status: GroupStatus | null
  users: TblUsers[]
}

export interface GroupRequest extends AuthorizedUserRequest {
  id: number
}

export interface GroupSearchRequest extends BaseSearchRequest {
  name: string
}

export interface UserGroupRequest extends AuthorizedUserRequest {
  userId: number
}
