import express from 'express'
import userRouter from './user.route'
import authRouter from './auth.route'
import linkRouter from './link.route'
import locationRouter from './location.route'
import gameRouter from './game.route'
import gameG1Router from './gameG1.route'
import waterGameRouter from './waterGame.route'
import luckyWheelRouter from './luckyWheel.route'
import pollRouter from './poll.route'

const router = express.Router()

router.use(userRouter)
router.use(authRouter)
router.use(linkRouter)
router.use(locationRouter)
router.use(gameRouter)
router.use(gameG1Router)
router.use(waterGameRouter)
router.use(luckyWheelRouter)
router.use(pollRouter)

export const apiRouter = router
