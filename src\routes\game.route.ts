import express from 'express'
import { gameAP<PERSON> } from '@/api'
import { authenticate} from '@/middlewares/auth'

// my contents router
const myGamesRouter = express.Router()
// myNotificationsRouter.use(authenticate)

myGamesRouter.get('/', authenticate, gameAPI.gameListHandler)
myGamesRouter.get('/:id', authenticate, gameAPI.infoGameHandler)
myGamesRouter.get('/:id/gifts', authenticate, gameAPI.listGiftHandler)
myGamesRouter.post('/:id/reward', authenticate, gameAPI.rewardHandler)
myGamesRouter.get('/:id/winners', gameAPI.winnersHandler)
myGamesRouter.get('/:id/myGifts', authenticate, gameAPI.myGiftsHandler)
myGamesRouter.get('/:id/logs', authenticate, gameAPI.gameLogsHandler)

// Export router
const router = express.Router()
router.use('/games', myGamesRouter)

export default router

