# 🎮 Tokyo Life Game G1 - API Usage Guide


### 1. 🚀 **Initialize Game**

Khởi tạo game, preload user data và game UI. User sẽ nhận thêm 1 lượt chơi mỗi ngày mới.

**Example Request:**
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/game/g1/init' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI1YzMxMzBhMC1kOTg2LTRjODEtYjhmZC1kM2RlOGQyZmFjMTUiLCJ0eXAiOiJhY2Nlc3NfdG9rZW4iLCJpZCI6IjI1NDQyIiwicm9sZSI6MTIsImlzcyI6InVzZXItc2VydmljZSIsImV4cCI6MTc1MjcyMDEyMiwiaWF0IjoxNzUyMTE1MzIyfQ.d8bG9GBmlvYyuw6XHSGYpqazfh-XcS_H23OGUNeVSnY'
```

**Response Success:**
```json
{
    "status": "success",
    "user": {
        "id": "25442",
        "name": "<PERSON>uyễn <PERSON>"
    },
    "play_turns": 1
}
```

### 2. 🎲 **Start Game**

Bắt đầu game và lấy danh sách vouchers sẽ xuất hiện trong game.


**Example Request:**
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/game/g1/play' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI1YzMxMzBhMC1kOTg2LTRjODEtYjhmZC1kM2RlOGQyZmFjMTUiLCJ0eXAiOiJhY2Nlc3NfdG9rZW4iLCJpZCI6IjI1NDQyIiwicm9sZSI6MTIsImlzcyI6InVzZXItc2VydmljZSIsImV4cCI6MTc1MjcyMDEyMiwiaWF0IjoxNzUyMTE1MzIyfQ.d8bG9GBmlvYyuw6XHSGYpqazfh-XcS_H23OGUNeVSnY'
```

**Response Success:**
```json
{
    "status": "success",
    "play_turns": 2,
    "vouchers": [
        {
            "id": 10431,
            "value": "KAM - Kho Voucher",
            "image": "https://kam-gift.mediacdn.vn/kam-voucher-10.png"
        },
        {
            "id": 10432,
            "value": "KAM - Voucher 20%",
            "image": "https://kam-gift.mediacdn.vn/kam-voucher-20.png"
        },
        {
            "id": 10433,
            "value": "KAM - Voucher 30%",
            "image": "https://kam-gift.mediacdn.vn/kam-voucher-30.png"
        }
    ]
}
```

### 3. 🎁 **Claim Voucher**

**Request Body:**
```json
{
  "voucher_id": 2
}
```

**Example Request:**
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/game/g1/claim' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI1YzMxMzBhMC1kOTg2LTRjODEtYjhmZC1kM2RlOGQyZmFjMTUiLCJ0eXAiOiJhY2Nlc3NfdG9rZW4iLCJpZCI6IjI1NDQyIiwicm9sZSI6MTIsImlzcyI6InVzZXItc2VydmljZSIsImV4cCI6MTc1MjcyMDEyMiwiaWF0IjoxNzUyMTE1MzIyfQ.d8bG9GBmlvYyuw6XHSGYpqazfh-XcS_H23OGUNeVSnY' \
--header 'Content-Type: application/json' \
--data '{
    "voucher_id": 10431
}'
```

**Response Success (Còn lượt chơi):**
```json
{
    "status": "success",
    "message": "Bạn nhận được Voucher giảm giá 10%",
    "play_turns": 0
}
```

**Response Success (Hết voucher):**
```json
{
  "status": "false",
  "message": "Đã hết voucher này",
  "play_turns": 2
}
```

**Response (Hết lượt chơi):**
```json
{
  "status": "no_turns",
  "message": "Bạn đã hết lượt chơi hôm nay",
  "received_rewards": [
    {
      "id": 1,
      "name": "Voucher 50K"
    },
    {
      "id": 2, 
      "name": "Voucher 100K"
    }
  ],
  "share_remaining": 1
}
```

### 4. 📤 **Share for Extra Turn**


**Example Request:**
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/game/g1/share' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI1YzMxMzBhMC1kOTg2LTRjODEtYjhmZC1kM2RlOGQyZmFjMTUiLCJ0eXAiOiJhY2Nlc3NfdG9rZW4iLCJpZCI6IjI1NDQyIiwicm9sZSI6MTIsImlzcyI6InVzZXItc2VydmljZSIsImV4cCI6MTc1MjcyMDEyMiwiaWF0IjoxNzUyMTE1MzIyfQ.d8bG9GBmlvYyuw6XHSGYpqazfh-XcS_H23OGUNeVSnY' \
--header 'Content-Type: application/json' \
--data '{
    "platform": "facebook"
}'
```

**Response Success:**
```json
{
  "status": "success",
  "message": "Bạn đã nhận thêm 1 lượt chơi!",
  "play_turns": 3
}
```

**Response (Đã share hôm nay):**
```json
{
  "status": "already_shared",
  "message": "Bạn đã chia sẻ và nhận lượt hôm nay rồi"
}
```

**Response (Platform không hỗ trợ):**
```json
{
  "status": "error",
  "message": "Không hỗ trợ platform này"
}
```

## 🚨 Error Handling

### Common Error Responses:

**Missing Parameters:**
```json
{
  "status": "error",
  "message": "Missing game slug"
}
```

**Authentication Error:**
```json
{
  "status": "error",
  "message": "Unauthorized"
}
```
*Last updated: 2024-01-15* 