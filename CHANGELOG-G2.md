# Changelog - Game G2 Logic Update

## Thay đổi chính trong `src/services/gameG1.service.ts`

### 1. Logic Random Prize (Dòng 267-272)
```typescript
// Random prize giống G1 để xác định loại phần thưởng chính
const randomizedPrize = luckyWheelService.randomPrize(prizes);

if (!randomizedPrize) {
  throw new NotFoundError('Không có phần thưởng nào', '', 1)
}
```

**Thay đổi**: Sử dụng `luckyWheelService.randomPrize(prizes)` giống G1 thay vì logic cũ.

### 2. Winning Box Logic (Dòng 274-283)
```typescript
// Xác định số box trúng dựa trên MAX_WINNING_BOXES và prize type
let actualWinningBoxCount = 0;
if (MAX_WINNING_BOXES > 0 && randomizedPrize.type === 'voucher') {
  actualWinningBoxCount = Math.min(MAX_WINNING_BOXES, sentBoxIds.length);
}

// Chọn random winning boxes từ sentBoxIds
const winningBoxes = [];
if (actualWinningBoxCount > 0) {
  const remainingSentBoxes = [...sentBoxIds];
  for (let i = 0; i < actualWinningBoxCount; i++) {
    const randomIndex = Math.floor(Math.random() * remainingSentBoxes.length);
    winningBoxes.push(remainingSentBoxes.splice(randomIndex, 1)[0]);
  }
}
```

**Thay đổi**: 
- Chỉ có box trúng khi `randomizedPrize.type === 'voucher'` và `MAX_WINNING_BOXES > 0`
- Random chọn box trúng từ danh sách `sentBoxIds`

### 3. Prize Distribution (Dòng 285-346)
```typescript
// Lưu trữ các prize đã sử dụng để tránh lặp lại
const usedPrizeIds = new Set();

// Fill các box được gửi lên
for (const boxId of sentBoxIds) {
  const boxIndex = boxId - 1;
  
  if (winningBoxes.includes(boxId)) {
    // Box trúng - sử dụng voucher prizes
    const availableWinPrizes = winPrizes.filter(p => !usedPrizeIds.has(p.id));
    const prizeToUse = availableWinPrizes.length > 0 ? availableWinPrizes : winPrizes;
    // ... assign winning prize
  } else {
    // Box không trúng - sử dụng lose prizes
    const availableLosePrizes = losePrizes.filter(p => !usedPrizeIds.has(p.id));
    // ... assign losing prize
  }
}

// Fill các box còn lại bằng tất cả prizes không trùng
const allPrizes = [...winPrizes, ...losePrizes];
const availablePrizesForRemaining = allPrizes.filter(p => !usedPrizeIds.has(p.id));
// ... fill remaining boxes
```

**Thay đổi**:
- Sử dụng `usedPrizeIds` thay vì `usedPrizeNames` để tránh trùng lặp chính xác hơn
- Box trúng: Chỉ sử dụng voucher prizes (type !== 'lose')
- Box không trúng trong danh sách gửi lên: Ưu tiên lose prizes
- Box còn lại: Random từ tất cả prizes không trùng

### 4. Coupon Processing (Dòng 382-428)
```typescript
if (winningBoxes.length > 0) {
  for (const winningBoxId of winningBoxes) {
    const winningBox = boxes.find(box => box.id === winningBoxId && box.prizeData);
    if (winningBox && winningBox.prizeData) {
      const winningPrize = winningBox.prizeData;
      let couponData = null;
      
      // Xử lý coupon cho từng box riêng biệt
      if (winningPrize.type === 'voucher' && winningPrize.bizStorageId) {
        // ... process coupon
      }
      
      // Lưu SpinHistory cho mỗi box trúng
      await luckyWheelService.saveSpinHistory(user.id, winningPrize.id || 0, campaignId, gameId, couponData)
    }
  }
}
```

**Thay đổi**:
- Xử lý coupon cho từng winning box riêng biệt
- Lưu SpinHistory cho mỗi box trúng thay vì chung
- Cải thiện error handling

### 5. Configuration
```typescript
// Cấu hình số box có thể trúng cùng lúc:
// 0 = không box nào trúng
// 1 = bắt buộc có 1 box gửi lên trúng  
// 2 = bắt buộc có 2 box gửi lên trúng
// v.v...
const MAX_WINNING_BOXES = 1;
```

**Thay đổi**: Thêm comment hướng dẫn cấu hình `MAX_WINNING_BOXES`.

## Tóm tắt cải tiến

1. **Tuân theo random prize**: Logic giờ phụ thuộc vào kết quả `randomPrize()` giống G1
2. **Flexible winning control**: Có thể config số box trúng qua `MAX_WINNING_BOXES`
3. **Better prize distribution**: Tránh trùng lặp prizes bằng `usedPrizeIds`
4. **Individual coupon processing**: Xử lý coupon cho từng box riêng biệt
5. **Improved error handling**: Xử lý lỗi tốt hơn và không log ra console
6. **Cleaner code structure**: Code dễ đọc và maintain hơn

## Test Cases

- [x] MAX_WINNING_BOXES = 0: Không box nào trúng
- [x] MAX_WINNING_BOXES = 1: 1 box trúng
- [x] MAX_WINNING_BOXES = 2: 2 box trúng  
- [x] Random prize type = 'lose': Không box nào trúng
- [x] Prize distribution không trùng lặp
- [x] Coupon processing cho multiple boxes
