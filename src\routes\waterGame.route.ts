import express from 'express'
import { waterGameAPI } from '@/api'
import { authenticate } from '@/middlewares/auth'

const router = express.Router()
router.post('/water-game/load', authenticate, waterGameAPI.loadWaterGameHandler)
router.post('/water-game/water', authenticate, waterGameAPI.waterHandler)
router.get('/water-game/reward-codes', authenticate, waterGameAPI.getUserRewardCodesHandler)
router.get('/water-game/today-stats', waterGameAPI.getTodayStatsHandler)
router.post('/water-game/draw-gift', authenticate, waterGameAPI.drawGiftHandler)
// router.post('/water-game/create-from-logs', authenticate, waterGameAPI.createFromLogsHandler)

export default router 