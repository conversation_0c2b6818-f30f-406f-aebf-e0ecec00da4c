/*
SQLyog Community v13.3.0 (64 bit)
MySQL - 8.0.33-25 : Database - tokyolife-game
*********************************************************************
*/

/*!40101 SET NAMES utf8 */;

/*!40101 SET SQL_MODE=''*/;

/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
CREATE DATABASE /*!32312 IF NOT EXISTS*/`tokyolife-game` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci */ /*!80016 DEFAULT ENCRYPTION='N' */;

/*Table structure for table `warter_user_gift_award` */

DROP TABLE IF EXISTS `warter_user_gift_award`;

CREATE TABLE `warter_user_gift_award` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `warter_user_gift_id` int NOT NULL DEFAULT '0',
  `user_id` int NOT NULL DEFAULT '0',
  `is_win` tinyint DEFAULT '0',
  `type_win` int DEFAULT '0',
  `created_by` bigint DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ix_user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;

/*Data for the table `warter_user_gift_award` */

insert  into `warter_user_gift_award`(`id`,`warter_user_gift_id`,`user_id`,`is_win`,`type_win`,`created_by`,`created_at`,`updated_at`) values 
(1,128,15887,1,1,NULL,'2025-06-23 16:17:43','2025-06-23 16:17:43'),
(2,381,25358,1,1,NULL,'2025-06-23 16:17:48','2025-06-23 16:17:48'),
(3,126,15871,1,1,NULL,'2025-06-26 14:35:15','2025-06-26 14:35:15'),
(4,105,15906,1,1,NULL,'2025-06-26 14:35:19','2025-06-26 14:35:19'),
(5,385,15902,1,1,NULL,'2025-06-26 14:35:21','2025-06-26 14:35:21'),
(6,73,15890,1,1,NULL,'2025-06-26 14:35:23','2025-06-26 14:35:23');

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
