import { REDIS } from '@/config/config'
import logger from '@/config/logger'
import Redis from 'ioredis'

class RedisService {
  private static _shared: RedisService

  private readonly client: Redis.Redis | Redis.Cluster

  private constructor() {
    if (REDIS.CLUSTER_MODE) {
      logger.info('Redis client will be running in cluster mode.')
      this.client = new Redis.Cluster([
        {
          port: REDIS.CLUSTER_PORT,
          host: REDIS.CLUSTER_HOST
        }
      ])
    } else {
      logger.info('Redis client will be running in running non cluster mode.')
      this.client = new Redis(REDIS.URI)
    }
    this.client.on('error', (err) => logger.error('Redis Client Error', err))
  }

  public getClient() {
    return this.client
  }

  public static get Shared() {
    return this._shared || (this._shared = new this())
  }
}

export default RedisService
