import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'

@Entity('poll_article_links')
export class TblPollArticleLinks extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id' })
  id: number

  @Column('int', { name: 'poll_id' })
  pollId: number

  @Column('varchar', { name: 'title', length: 255 })
  title: string

  @Column('varchar', { name: 'url'})
  url: string

  @Column('tinyint', { name: 'status' })
  status: number
}