import { Column, <PERSON>ti<PERSON>, PrimaryGeneratedColumn, ManyToOne, JoinColumn } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'
import { TblUsers } from './TblUsers'
import { TblGifts } from './TblGifts'
import { TblGames } from './TblGames'

@Entity('user_event_gifts', { schema: 'events' })
export class TblUserEventGift extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('int', { name: 'user_id', nullable: false })
  userId: number

  @Column('int', { name: 'event_id', nullable: false })
  eventId: number

  @Column('int', { name: 'gift_id', nullable: false })
  giftId: number

  @ManyToOne(() => TblUsers, { eager: false })
  @JoinColumn({ name: 'user_id' })
  user: TblUsers

  @ManyToOne(() => TblGames, { eager: false })
  @JoinColumn({ name: 'event_id' })
  event: TblGames

  @ManyToOne(() => TblGifts, { eager: false })
  @JoinColumn({ name: 'gift_id' })
  gift: TblGifts
}
