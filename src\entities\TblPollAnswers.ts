import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'

@Entity('poll_answer')
export class TblPollAnswers extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id' })
  id: number

  @Column('int', { name: 'poll_id' })
  pollId: number

  @Column('varchar', { name: 'name', length: 255 })
  name: string

  @Column('int', { name: 'display_order' })
  displayOrder: number
}