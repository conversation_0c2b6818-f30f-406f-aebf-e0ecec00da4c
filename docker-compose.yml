version: '3.8'
services:
  backend:
    container_name: content-ai-backend
    build:
      context: .
      dockerfile: Dockerfile
    depends_on:
      - db
    volumes:
      - ./:/app
      - /app/node_modules
    environment:
      - APP_ENV=dev
      - APP_PORT=3001
      - DB_USER=contentai
      - DB_PASSWORD=123abcA@
      - DB_NAME=contentAI
      - DB_PORT=3306
      - DB_HOST=db
      - REDIS_URI=redis://redis
    env_file:
      - .env
    ports:
      - '3001:3001'
  db:
    image: mysql:8.0.23
    container_name: content-ai-dev-db
    command: --default-authentication-plugin=mysql_native_password
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: admin
      MYSQL_USER: contentai
      MYSQL_PASSWORD: 123abcA@
      MYSQL_DATABASE: contentAI
    volumes:
      - ./mysql/mysqlconf.cnf:/etc/mysql/conf.d/mysqlconf.cnf
    ports:
      - 13306:3306
  redis:
    image: redis
    container_name: content-ai-dev-redis
    command: redis-server
    ports:
      - 6379:6379
    volumes:
      - ./volumes/redis/data:/data
