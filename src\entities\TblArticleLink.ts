import { Column, Entity, PrimaryGeneratedColumn, ManyToMany, OneToMany } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'
import { TblUserArticleLink } from './TblUserArticleLink'

@Entity('article_links', { schema: 'events' })
export class TblArticleLink extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('varchar', { name: 'link', length: 255 })
  link: string

  @Column('text', { name: 'title' })
  title: string

  @Column('int', { name: 'active', nullable: false })
  active: number

  @Column('int', { name: 'read', nullable: false })
  read: number

  @OneToMany(() => TblUserArticleLink, (userArticleLink) => userArticleLink.articleLink)
  userArticleLinks: TblUserArticleLink[]
}
