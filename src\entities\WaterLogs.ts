import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'

@Entity('water_logs')
export class WaterLogs extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id' })
  id: number

  @Column('int', { name: 'user_id' })
  userId: number

  @Column('int', { name: 'game_id' })
  gameId: number

  @Column('int', { name: 'water_gift_config_id' })
  waterGiftConfigId: number

  @Column('int', { name: 'warter_user_gift_id' })
  warterUserGiftId: number

  @Column('tinyint', { name: 'gift_received' })
  giftReceived: number
} 