import { Column, <PERSON>tity, PrimaryGeneratedColumn, ManyToOne, JoinColumn } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'
import { TblUsers } from './TblUsers'
import { VoucherDraw } from './VoucherDraw'

@Entity('user_gift_award')
export class UserGiftAward extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id' })
  id: number

  @Column('int', { name: 'voucher_draw_id', default: 0 })
  voucherDrawId: number

  @Column('int', { name: 'user_id', default: 0 })
  userId: number

  @Column('tinyint', { name: 'is_win', default: 0 })
  isWin: number

  @Column('varchar', { name: 'phone', length: 255, default: '' })
  phone: string

  @Column('int', { name: 'prize_id', default: 0 })
  prizeId: number

  @Column('int', { name: 'campaign_id' })
  campaignId: number

  @Column('bigint', { name: 'created_by', nullable: true })
  createdBy: number | null

  // Relationships
  @ManyToOne(() => VoucherDraw)
  @JoinColumn({ name: 'voucher_draw_id' })
  voucherDraw: VoucherDraw

  @ManyToOne(() => TblUsers)
  @JoinColumn({ name: 'user_id' })
  user: TblUsers
} 