import { Column, Entity, PrimaryGeneratedColumn} from 'typeorm'
import { BaseEntity } from './base/BaseEntity'

@Entity('commune', { schema: 'events' })
export class TblCommunes extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('varchar', { name: 'name', length: 255 })
  name: string

  @Column('varchar', { name: 'slug', length: 500 })
  slug: string

  @Column('int', { name: 'district_id', nullable: false })
  districtId: number

  //created_at

  //updated_at
}
