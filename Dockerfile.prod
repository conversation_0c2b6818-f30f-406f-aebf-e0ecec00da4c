#################################################
# 🔧Build
#################################################
FROM node:16.17.0-alpine as build
WORKDIR /app

# ENVs
ENV PATH /app/node_modules/.bin:$PATH
ENV APP_PORT=80

# Copy files
COPY ./src /app/src
COPY ./resources /app
COPY ./package.json /app
COPY ./tsconfig.json /app

# Build
RUN yarn install
RUN yarn run build

# Clean
RUN rm -rf /app/src

LABEL Name=api-event-admicro-vn-prod Version=0.0.1

#################################################
# 🚀Run
#################################################
FROM node:16.17.0-alpine

WORKDIR /app
COPY --from=build /app /app

EXPOSE ${APP_PORT}

CMD ["yarn", "start"]