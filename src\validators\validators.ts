import { type Validation<PERSON>hai<PERSON> } from 'express-validator'
import { IsIntOptions, MinMaxOptions } from 'express-validator/src/options'

export type Validation = ValidationChain

// email validator
export const emailValidator = (validation: ValidationChain) => validation.isEmail()

// JWT validator
export const jwtValidator = (validation: ValidationChain) => validation.isJWT()

// Text validator
export type TextValidatorOptions = {
  lengthOption: MinMaxOptions
}
export const textValidator = (validation: Validation<PERSON>hain, options: TextValidatorOptions) => validation.isLength(options.lengthOption)

export const optionalTextValidator = (validation: ValidationChain, options: TextValidatorOptions) =>
  validation.isString().isLength(options.lengthOption).optional({ nullable: true })

export const requiredTextValidator = (validation: ValidationChain, options: TextValidatorOptions, ignore_whitespace = true) =>
  textValidator(validation, options).isString().notEmpty({ ignore_whitespace })

export const requiredAlphaTextValidator = (validation: ValidationChain, options: TextValidatorOptions) => requiredTextValidator(validation, options).isAlpha()

// Password validator
export const passwordTextValidator = (validation: ValidationChain) =>
  requiredTextValidator(validation, { lengthOption: { max: 64 } }).isStrongPassword({
    minLength: 8,
    minLowercase: 0,
    minUppercase: 0,
    minNumbers: 0,
    minSymbols: 0
  })

export const requiredValidator = (validation: ValidationChain) => validation.notEmpty()

export type DecimalValidatorOptions = {
  lt: number
  decimal_digits: string
}

export const decimalValidator = (validation: ValidationChain, options: DecimalValidatorOptions) =>
  validation.isFloat({ lt: options.lt }).isDecimal({ decimal_digits: options.decimal_digits })
export const optionalDecimalValidator = (validation: ValidationChain, options: DecimalValidatorOptions) => decimalValidator(validation, options).optional({ nullable: true })
export const isArrayValidator = (validation: ValidationChain) => validation.isArray()
export const datetimeValidator = (validation: ValidationChain) => validation.isISO8601()
export const isIntValidator = (validation: ValidationChain, options?: IsIntOptions) => validation.isInt(options)
export const isBooleanValidator = (validation: ValidationChain) => validation.isBoolean()

// validate bot name, allow hyphen, alphabet
export const botNameValidator = (validation: ValidationChain) => validation.isString().matches(/^[a-zA-Z0-9-]+$/)
