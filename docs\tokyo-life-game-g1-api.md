# Tokyo Life Game - API Documentation - G1

## Authentication
All requests require authentication via <PERSON><PERSON>.

**Header Example:**

```

Authorization: Bearer <token>

````

---

T<PERSON>t cả truyền vào truyền vào gameId dạng slug có 1 switch case để chuyển về gameId dạng số

## 1. GET `/api/game/init`

**Description:** Initialize game, preload user data and game UI.
sử dụng AuthorizedUserRequest trả về thông tin user và lượt chơi còn lại trong UserSpin

**Response:**
```json
{
  "status": "success",
  "user": {
    "id": 123,
    "name": "<PERSON>uy<PERSON>"
  },
  "play_turns": 3
}
````

---

## 2. GET `/api/game/play`

**Description:** Start the game and get vouchers that will fall.
sử dụng AuthorizedUserRequest trả về thông tin danh sách phần quà trong LuckyPrize và lượt chơi còn lại trong UserSpin
**Response:**

```json
{
  "status": "success",
  "play_turns": 3,
  "vouchers": [
    {"id": 1, "value": "50K", "image": "/assets/voucher_50.png"},
    {"id": 2, "value": "100K", "image": "/assets/voucher_100.png"}
  ]
}
```

---

## 3. POST `/api/game/claim`

**Description:** Claim a voucher by tapping it.
sử dụng AuthorizedUserRequest voucher_id tương đương với biz_storage_id trong LuckyPrize lưu vào SpinHistory
và gọi getCouponFromBizfly trong luckyWheelService để lấy coupon và lưu vào và trả về message là voucher_name
share_remaining là lượt chơi được cộng trong ngày nếu hôm nay đã cộng thì sẽ là 0 trong UserSpin nếu lastRequest là ngày hôm nay thì là đã cộng. còn không phải thì là chưa cộng
**Request:**

```json
{
  "voucher_id": 2
}
```

**Response (if turns remaining):**

```json
{
  "status": "success",
  "message": "Bạn nhận được Voucher 100K",
  "play_turns": 2
}
```

**Response (if no turns left):**

```json
{
  "status": "no_turns",
  "message": "Bạn đã hết lượt chơi hôm nay",
  "received_rewards": [
    {"id": 1, "name": "Voucher 50K"},
    {"id": 2, "name": "Voucher 100K"}
  ],
  "share_remaining": 1
}
```

---

## 4. POST `/api/game/share`

**Description:** Share to receive 1 extra play turn (1 time per day).
sử dụng AuthorizedUserRequest dựa theo lastRequest và platform để cộng thêm lượt
**Request:**

```json
{
  "platform": "facebook"
}
```

**Response:**

```json
{
  "status": "success",
  "message": "Bạn đã nhận thêm 1 lượt chơi!",
  "play_turns": 1
}
```

**If already shared today:**

```json
{
  "status": "already_shared",
  "message": "Bạn đã chia sẻ và nhận lượt hôm nay rồi"
}
```

---

## Notes

* **Play turns reset daily** at 00:00.
* **Share for extra turn** is limited to once per day.
* **Voucher claim is only valid if `play_turns` > 0.**
