import { Column, Entity, PrimaryGeneratedColumn, ManyToMany, JoinTable, OneToMany } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'

export enum GroupStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE'
}

@Entity('groups', { schema: 'events' })
export class TblGroups extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('varchar', { name: 'name', nullable: false, length: 2048 })
  name: string | null

  @Column('varchar', { name: 'description', nullable: false, length: 2048 })
  description: string | null

  @Column('varchar', { name: 'code', nullable: false, length: 120 })
  code: string | null

  @Column('enum', {
    name: 'status',
    nullable: true,
    enum: ['ACTIVE', 'INACTIVE']
  })
  status: GroupStatus | null
}
