import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn } from 'typeorm'

@Entity('voucher_draw')
export class VoucherDraw {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id!: number

  @Column({ name: 'user_id', type: 'bigint', nullable: true })
  userId?: number

  @Column({ name: 'campaign_id', type: 'int', nullable: true })
  campaignId?: number

  @Column({ name: 'game_id', type: 'int', nullable: true })
  gameId?: number

  @Column({ type: 'varchar', length: 128, nullable: true })
  phone?: string

  @Column({ name: 'voucher_code', type: 'varchar', length: 255, nullable: true })
  voucherCode?: string

  @Column({ name: 'storage_id', type: 'varchar', length: 255, nullable: true })
  storageId?: string

  @CreateDateColumn({ name: 'created_at', type: 'timestamp', nullable: true })
  createdAt?: Date
} 